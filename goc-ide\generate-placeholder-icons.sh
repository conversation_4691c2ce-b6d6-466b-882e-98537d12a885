#!/usr/bin/env bash
# Generate placeholder icons for GOC IDE using ImageMagick

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

echo -e "${BLUE}🎨 Generating GOC IDE placeholder icons...${NC}\n"

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    print_warning "ImageMagick not found. Please install it to generate icons:"
    echo "  Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "  macOS: brew install imagemagick"
    echo "  Windows: Download from https://imagemagick.org/script/download.php#windows"
    echo ""
    print_info "Alternatively, create icons manually using the specifications in ICON_REQUIREMENTS.md"
    exit 1
fi

# Create directories
mkdir -p goc-branding/icons/{win32,darwin,linux,web}

# GOC IDE colors
PRIMARY_COLOR="#007ACC"
ACCENT_COLOR="#00D4AA"
BACKGROUND_COLOR="#1E1E1E"

print_status "Creating base icon design..."

# Create a simple GOC logo design using ImageMagick
create_base_icon() {
    local size=$1
    local output=$2
    
    # Create a circular icon with GOC text
    convert -size ${size}x${size} xc:"$BACKGROUND_COLOR" \
        -fill "$PRIMARY_COLOR" \
        -draw "circle $((size/2)),$((size/2)) $((size/2)),$((size/10))" \
        -fill "$ACCENT_COLOR" \
        -pointsize $((size/4)) \
        -gravity center \
        -annotate +0+0 "GOC" \
        -fill white \
        -pointsize $((size/8)) \
        -annotate +0+$((size/4)) "IDE" \
        "$output"
}

# Generate PNG icons for Linux
print_status "Generating Linux icons..."
sizes=(16 24 32 48 64 128 256 512)
for size in "${sizes[@]}"; do
    create_base_icon $size "goc-branding/icons/linux/code-${size}.png"
    print_info "Created ${size}x${size} Linux icon"
done

# Create main Linux icon (512x512)
cp "goc-branding/icons/linux/code-512.png" "goc-branding/icons/linux/code.png"

# Generate Windows ICO (using largest PNG and converting)
print_status "Generating Windows icon..."
create_base_icon 256 "goc-branding/icons/win32/temp-256.png"
convert "goc-branding/icons/win32/temp-256.png" \
    -resize 256x256 "goc-branding/icons/win32/code-256.png"
convert "goc-branding/icons/win32/temp-256.png" \
    -resize 128x128 "goc-branding/icons/win32/code-128.png"
convert "goc-branding/icons/win32/temp-256.png" \
    -resize 64x64 "goc-branding/icons/win32/code-64.png"
convert "goc-branding/icons/win32/temp-256.png" \
    -resize 48x48 "goc-branding/icons/win32/code-48.png"
convert "goc-branding/icons/win32/temp-256.png" \
    -resize 32x32 "goc-branding/icons/win32/code-32.png"
convert "goc-branding/icons/win32/temp-256.png" \
    -resize 16x16 "goc-branding/icons/win32/code-16.png"

# Create ICO file with multiple sizes
convert "goc-branding/icons/win32/code-256.png" \
        "goc-branding/icons/win32/code-128.png" \
        "goc-branding/icons/win32/code-64.png" \
        "goc-branding/icons/win32/code-48.png" \
        "goc-branding/icons/win32/code-32.png" \
        "goc-branding/icons/win32/code-16.png" \
        "goc-branding/icons/win32/code.ico"

# Create Windows tile icons
create_base_icon 70 "goc-branding/icons/win32/code_70x70.png"
create_base_icon 150 "goc-branding/icons/win32/code_150x150.png"

# Clean up temporary files
rm "goc-branding/icons/win32/temp-256.png"
rm "goc-branding/icons/win32/code-"*.png

print_info "Created Windows ICO with multiple sizes"

# Generate macOS ICNS (requires additional tools, create PNG for now)
print_status "Generating macOS icon..."
create_base_icon 1024 "goc-branding/icons/darwin/code-1024.png"
print_info "Created 1024x1024 macOS icon (PNG format)"
print_warning "Convert to ICNS format using: iconutil -c icns code.iconset"

# Generate web icons
print_status "Generating web icons..."
create_base_icon 32 "goc-branding/icons/web/favicon-32.png"
create_base_icon 16 "goc-branding/icons/web/favicon-16.png"

# Create favicon.ico
convert "goc-branding/icons/web/favicon-32.png" \
        "goc-branding/icons/web/favicon-16.png" \
        "goc-branding/icons/web/favicon.ico"

# Create web app icon
create_base_icon 256 "goc-branding/icons/web/code-web.png"
convert "goc-branding/icons/web/code-web.png" "goc-branding/icons/web/code-web.ico"

print_info "Created web icons and favicon"

# Create installation script
cat > goc-branding/install-icons.sh << 'EOF'
#!/usr/bin/env bash
# Install GOC IDE icons to VS Code source directories

echo "Installing GOC IDE icons..."

# Check if we're in the right directory
if [ ! -d "resources" ]; then
    echo "Error: resources directory not found. Run this from VS Code source root."
    exit 1
fi

# Backup original icons
echo "Backing up original icons..."
mkdir -p icon-backups
cp -r resources/win32/* icon-backups/ 2>/dev/null || true
cp -r resources/darwin/* icon-backups/ 2>/dev/null || true
cp -r resources/linux/* icon-backups/ 2>/dev/null || true
cp -r resources/server/* icon-backups/ 2>/dev/null || true

# Install new icons
echo "Installing GOC IDE icons..."
cp goc-branding/icons/win32/* resources/win32/ 2>/dev/null || true
cp goc-branding/icons/darwin/* resources/darwin/ 2>/dev/null || true
cp goc-branding/icons/linux/* resources/linux/ 2>/dev/null || true
cp goc-branding/icons/web/* resources/server/ 2>/dev/null || true

echo "✓ GOC IDE icons installed successfully!"
echo "  Original icons backed up to icon-backups/"
echo "  Rebuild the application to see changes"
EOF

chmod +x goc-branding/install-icons.sh

print_status "Icon generation complete!"
print_info "Generated icons in goc-branding/icons/"
print_warning "These are placeholder icons. For production, create professional icons using:"
print_warning "  - Adobe Illustrator/Photoshop"
print_warning "  - Figma or Sketch"
print_warning "  - Professional icon design services"

echo ""
print_info "To install icons:"
echo "  cd goc-ide"
echo "  ./goc-branding/install-icons.sh"

echo ""
print_info "To create macOS ICNS file:"
echo "  mkdir code.iconset"
echo "  cp goc-branding/icons/darwin/code-1024.png code.iconset/<EMAIL>"
echo "  # Add other required sizes..."
echo "  iconutil -c icns code.iconset"

echo -e "\n${GREEN}🎉 Placeholder icons generated successfully!${NC}"
