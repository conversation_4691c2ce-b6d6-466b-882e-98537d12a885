<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ApiUsage extends Model
{
    use HasFactory;

    protected $table = 'api_usage';

    protected $fillable = [
        'user_id',
        'endpoint',
        'method',
        'status_code',
        'response_time_ms',
        'tokens_used',
        'provider',
        'model',
    ];

    /**
     * Get the user that owns the API usage record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
