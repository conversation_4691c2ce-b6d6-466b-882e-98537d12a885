<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AgentSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_uuid',
        'title',
        'provider',
        'model',
        'context_data',
        'memory_data',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'context_data' => 'array',
            'memory_data' => 'array',
        ];
    }

    /**
     * Get the user that owns the session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the messages for the session.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(SessionMessage::class, 'session_id');
    }
}
