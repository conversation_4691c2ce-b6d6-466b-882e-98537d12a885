<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usage_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('api_key_id')->nullable(); // Reference to user_api_keys
            $table->string('endpoint'); // API endpoint used
            $table->string('method', 10); // HTTP method
            $table->string('ip_address', 45);
            $table->text('user_agent')->nullable();
            $table->json('request_data')->nullable(); // Request payload (sanitized)
            $table->json('response_data')->nullable(); // Response data (sanitized)
            $table->integer('response_status'); // HTTP status code
            $table->integer('response_time_ms')->nullable(); // Response time in milliseconds
            $table->integer('tokens_used')->default(0); // AI tokens consumed
            $table->decimal('cost', 10, 6)->default(0); // Cost of the request
            $table->string('session_id')->nullable(); // For grouping related requests
            $table->timestamp('created_at');

            $table->index(['user_id', 'created_at']);
            $table->index(['endpoint', 'created_at']);
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usage_logs');
    }
};
