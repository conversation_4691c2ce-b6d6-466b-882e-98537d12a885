# GOC IDE Setup - VS Code Fork

This guide will help you set up the GOC IDE by forking and customizing VS Code.

## Prerequisites

Before starting, ensure you have:
- Node.js 18+ (LTS recommended)
- Python 3.x
- Git
- 4+ CPU cores, 6-8GB RAM

## Step 1: Fork VS Code Repository

1. Go to https://github.com/microsoft/vscode
2. Click the "Fork" button in the top right
3. Create a fork in your GitHub account

## Step 2: Clone Your Fork

```bash
# Navigate to goc-ide directory
cd goc-ide

# Clone your forked repository (replace YOUR_USERNAME)
git clone https://github.com/YOUR_USERNAME/vscode.git .

# Add upstream remote for updates
git remote add upstream https://github.com/microsoft/vscode.git

# Verify remotes
git remote -v
```

## Step 3: Install Dependencies

```bash
# Install all dependencies
npm install

# This may take 10-15 minutes on first run
```

## Step 4: Build VS Code

```bash
# Compile TypeScript and build
npm run compile

# Start development watch mode
npm run watch
```

## Step 5: Run VS Code

```bash
# Linux/Mac
./scripts/code.sh

# Windows
.\scripts\code.bat

# Or with specific folder
./scripts/code.sh /path/to/your/project
```

## Development Commands

```bash
# Watch for changes (run in separate terminal)
npm run watch

# Clean build
npm run clean-build

# Run tests
npm test

# Package for distribution
npm run package
```

## Next Steps

After successful setup:
1. Remove Microsoft branding and telemetry
2. Add GOC Agent branding and configuration
3. Integrate GOC Agent backend API
4. Build custom extensions for AI features
5. Customize welcome screen and UI

## Important Files for Customization

- `product.json` - Product configuration and branding
- `src/vs/platform/telemetry/` - Telemetry removal
- `resources/` - Icons, images, and assets
- `extensions/` - Built-in extensions
- `src/vs/workbench/` - Main workbench UI

## Troubleshooting

### Build Issues
- Ensure Node.js version is 18+
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check Python installation for native modules

### Memory Issues
- Increase Node.js memory: `export NODE_OPTIONS="--max-old-space-size=8192"`
- Close other applications during build

### Permission Issues
- On Linux/Mac: `chmod +x scripts/code.sh`
- Run with appropriate permissions

## Resources

- [VS Code Contributing Guide](https://github.com/microsoft/vscode/wiki/How-to-Contribute)
- [VS Code Build Instructions](https://github.com/microsoft/vscode/wiki/How-to-Contribute#build-and-run)
- [Extension Development](https://code.visualstudio.com/api)
