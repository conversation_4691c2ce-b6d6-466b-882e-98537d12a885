<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('code_embeddings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('project_path', 500);
            $table->string('file_path', 500);
            $table->string('content_hash', 64);
            $table->json('embedding_vector');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'project_path']);
            $table->index('content_hash');
            $table->index(['file_path', 'content_hash']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('code_embeddings');
    }
};
