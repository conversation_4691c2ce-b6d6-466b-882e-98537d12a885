#!/usr/bin/env bash
# Remove Microsoft branding and telemetry from VS Code fork for GOC IDE

set -ex

echo "🧹 Removing Microsoft branding and telemetry..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "product.json" ]; then
    print_error "product.json not found. Make sure you're in the VS Code source directory."
    exit 1
fi

print_status "Starting Microsoft branding removal..."

# 1. Remove telemetry URLs
print_status "Removing telemetry endpoints..."

# Replace Microsoft telemetry URLs with localhost
SEARCH="\.data\.microsoft\.com"
REPLACEMENT="s|//[^/]+\.data\.microsoft\.com|//0.0.0.0|g"

# Use ripgrep if available, otherwise fallback to grep
if command -v rg &> /dev/null; then
    rg --no-ignore -l "${SEARCH}" . | xargs -I {} sed -i -E "${REPLACEMENT}" "{}"
else
    grep -rl --exclude-dir=.git -E "${SEARCH}" . | xargs -I {} sed -i -E "${REPLACEMENT}" "{}"
fi

print_status "Telemetry endpoints removed"

# 2. Replace product.json with GOC IDE configuration
print_status "Updating product.json for GOC IDE..."

# Backup original product.json
cp product.json product.json.backup

# Create GOC IDE product.json
cat > product.json << 'EOF'
{
  "nameShort": "GOC IDE",
  "nameLong": "GOC IDE",
  "applicationName": "goc-ide",
  "dataFolderName": ".goc-ide",
  "win32MutexName": "gocide",
  "licenseName": "MIT",
  "licenseUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/LICENSE",
  "win32DirName": "GOC IDE",
  "win32NameVersion": "GOC IDE",
  "win32RegValueName": "GocIde",
  "win32AppUserModelId": "GocAgent.GocIde",
  "win32ShellNameShort": "GOC IDE",
  "darwinBundleIdentifier": "com.goc-agent.ide",
  "linuxIconName": "goc-ide",
  "reportIssueUrl": "https://github.com/YOUR_USERNAME/goc-ide/issues/new",
  "urlProtocol": "goc-ide",
  "extensionAllowedBadgeProviders": [
    "api.bintray.com",
    "api.travis-ci.com",
    "api.travis-ci.org",
    "app.fossa.io",
    "badge.buildkite.com",
    "badge.fury.io",
    "badgen.net",
    "badges.frapsoft.com",
    "badges.gitter.im",
    "cdn.travis-ci.com",
    "cdn.travis-ci.org",
    "ci.appveyor.com",
    "circleci.com",
    "codacy.com",
    "codeclimate.com",
    "codecov.io",
    "coveralls.io",
    "david-dm.org",
    "deepscan.io",
    "dev.azure.com",
    "docs.rs",
    "flat.badgen.net",
    "gemnasium.com",
    "githost.io",
    "gitlab.com",
    "godoc.org",
    "goreportcard.com",
    "img.shields.io",
    "isitmaintained.com",
    "nodesecurity.io",
    "opencollective.com",
    "snyk.io",
    "travis-ci.com",
    "travis-ci.org",
    "www.bithound.io",
    "www.versioneye.com"
  ],
  "extensionAllowedBadgeProvidersRegex": [
    "^https:\\/\\/github\\.com\\/[^/]+\\/[^/]+\\/(actions\\/)?workflows\\/.*badge\\.svg"
  ],
  "linkProtectionTrustedDomains": [
    "https://open-vsx.org"
  ],
  "documentationUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/README.md",
  "keyboardShortcutsUrlMac": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/shortcuts-mac.md",
  "keyboardShortcutsUrlLinux": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/shortcuts-linux.md",
  "keyboardShortcutsUrlWin": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/shortcuts-windows.md",
  "introductoryVideosUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/videos.md",
  "tipsAndTricksUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/tips.md",
  "twitterUrl": "https://twitter.com/goc_agent",
  "requestFeatureUrl": "https://github.com/YOUR_USERNAME/goc-ide/issues/new?template=feature_request.md",
  "extensionsGallery": {
    "serviceUrl": "https://open-vsx.org/vscode/gallery",
    "cacheUrl": "https://open-vsx.org/vscode/gallery",
    "itemUrl": "https://open-vsx.org/vscode/item",
    "controlUrl": "",
    "recommendationsUrl": ""
  }
}
EOF

print_status "product.json updated for GOC IDE"

# 3. Remove Microsoft-specific files and references
print_status "Removing Microsoft-specific files..."

# Remove Microsoft marketplace references
find . -name "*.ts" -o -name "*.js" -o -name "*.json" | xargs grep -l "marketplace.visualstudio.com" | while read -r file; do
    sed -i 's|marketplace\.visualstudio\.com|open-vsx.org|g' "$file"
    print_status "Updated marketplace references in $file"
done

# Remove Microsoft telemetry references
find . -name "*.ts" -o -name "*.js" | xargs grep -l "vortex\.data\.microsoft\.com\|mobile\.events\.data\.microsoft\.com" | while read -r file; do
    sed -i 's|vortex\.data\.microsoft\.com|0.0.0.0|g' "$file"
    sed -i 's|mobile\.events\.data\.microsoft\.com|0.0.0.0|g' "$file"
    print_status "Removed telemetry from $file"
done

print_status "Microsoft branding and telemetry removal complete!"
print_warning "Remember to:"
print_warning "1. Replace icons in resources/ directory"
print_warning "2. Update README and documentation"
print_warning "3. Replace YOUR_USERNAME with your actual GitHub username"
print_warning "4. Test the build to ensure everything works"

echo -e "${GREEN}🎉 GOC IDE branding removal completed successfully!${NC}"
