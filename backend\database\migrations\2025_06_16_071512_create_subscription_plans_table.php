<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Free, Pro, Enterprise
            $table->string('slug')->unique(); // free, pro, enterprise
            $table->text('description');
            $table->decimal('price', 8, 2); // Monthly price
            $table->decimal('yearly_price', 8, 2)->nullable(); // Yearly price (discounted)
            $table->string('stripe_price_id')->nullable(); // Stripe price ID
            $table->string('stripe_yearly_price_id')->nullable(); // Stripe yearly price ID
            $table->integer('api_requests_daily')->default(0); // 0 = unlimited
            $table->integer('api_requests_monthly')->default(0); // 0 = unlimited
            $table->integer('max_projects')->default(0); // 0 = unlimited
            $table->integer('max_team_members')->default(1);
            $table->json('features'); // Array of features
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
