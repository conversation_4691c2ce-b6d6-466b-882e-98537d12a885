# GOC Agent Branding Guide

This guide explains how to apply GOC Agent branding to your VS Code fork.

## Overview

The branding process includes:
1. **Visual Identity**: Icons, colors, and themes
2. **Application Names**: Titles, window names, and identifiers
3. **Welcome Experience**: Custom welcome screen and splash
4. **UI Customization**: Activity bar, status bar, and panels
5. **Theme Integration**: Custom color schemes and styling

## Quick Start

```bash
# Make the script executable
chmod +x apply-branding.sh

# Apply GOC Agent branding
./apply-branding.sh
```

## Brand Identity

### Color Palette
- **Primary Blue**: `#007ACC` - Main brand color
- **Accent Green**: `#00D4AA` - Secondary/accent color
- **Dark Background**: `#1E1E1E` - Primary background
- **Light Text**: `#FFFFFF` - Primary text color
- **Medium Gray**: `#CCCCCC` - Secondary text color

### Typography
- **Primary Font**: Segoe UI, system fonts
- **Code Font**: Consolas, Monaco, monospace
- **Weights**: Light (300), Regular (400), Semibold (600)

## Visual Elements

### Icons
The script creates placeholders for all required icon formats:

**Windows (.ico)**
- Main application icon with multiple sizes
- Tile icons for Windows Store integration

**macOS (.icns)**
- Retina-ready application icon
- Multiple resolutions for different contexts

**Linux (.png)**
- Various sizes for different desktop environments
- Scalable vector-based designs preferred

**Web (.ico, .png)**
- Browser favicon and web app icons

### Splash Screen
Custom loading experience with:
- GOC IDE branding
- Progressive loading messages
- Branded color scheme
- Smooth animations

### Welcome Screen
Personalized onboarding featuring:
- GOC Agent introduction
- Feature highlights
- Quick action buttons
- Getting started guidance

## UI Customization

### Activity Bar
- GOC blue gradient background
- White foreground icons
- Green accent for badges and notifications

### Status Bar
- Horizontal blue-to-green gradient
- White text for visibility
- Hover effects with accent colors

### Title Bar
- Dark background with blue border
- Consistent with overall theme
- GOC IDE branding in window title

### Editor
- Dark theme optimized for coding
- Blue accent for line numbers and cursor
- Green highlights for selections

## Theme Configuration

### GOC Agent Dark Theme
Complete color scheme including:
- Editor colors and syntax highlighting
- UI element colors and borders
- Interactive element states
- Accessibility considerations

### Custom CSS
Additional styling for:
- Welcome page enhancements
- Chat panel integration
- Button and input customization
- Animation and transition effects

## Implementation Steps

### 1. Run Branding Script
```bash
./apply-branding.sh
```

### 2. Create Icon Assets
Follow the requirements in `goc-branding/ICON_REQUIREMENTS.md`:
- Design icons in multiple formats
- Ensure consistency across platforms
- Test visibility at different sizes

### 3. Copy Icon Files
```bash
# After creating icons
cp goc-branding/icons/win32/* resources/win32/
cp goc-branding/icons/darwin/* resources/darwin/
cp goc-branding/icons/linux/* resources/linux/
cp goc-branding/icons/web/* resources/server/
```

### 4. Customize Styles
Edit `goc-branding/goc-theme.css` to:
- Adjust colors and gradients
- Modify spacing and typography
- Add custom animations
- Enhance visual hierarchy

### 5. Test and Iterate
```bash
# Build and test
npm run compile
./scripts/code.sh

# Verify branding appears correctly
# Test on different platforms
# Gather feedback and iterate
```

## File Structure

```
goc-branding/
├── icons/
│   ├── win32/          # Windows icons
│   ├── darwin/         # macOS icons
│   ├── linux/          # Linux icons
│   └── web/            # Web icons
├── splash/             # Splash screen assets
├── themes/             # Color themes
├── assets/             # Additional assets
├── goc-theme.css       # Custom CSS styles
├── goc-theme.json      # Theme configuration
└── ICON_REQUIREMENTS.md # Icon specifications
```

## Customization Options

### Colors
Modify the color palette in:
- `goc-branding/goc-theme.json`
- `goc-branding/goc-theme.css`
- `src/vs/workbench/browser/parts/splash/gocSplash.ts`

### Welcome Content
Update welcome screen in:
- `src/vs/workbench/contrib/welcome/page/browser/gocWelcome.ts`

### Splash Messages
Customize loading messages in:
- `src/vs/workbench/browser/parts/splash/gocSplash.ts`

## Testing Checklist

- [ ] Application launches with GOC IDE branding
- [ ] Icons appear correctly on all platforms
- [ ] Welcome screen shows GOC Agent content
- [ ] Splash screen displays custom messages
- [ ] Activity bar uses GOC color scheme
- [ ] Status bar shows gradient background
- [ ] Title bar displays "GOC IDE"
- [ ] Theme colors are consistent
- [ ] All text is readable and accessible
- [ ] Hover effects work properly

## Troubleshooting

### Icons Not Appearing
- Check file paths and formats
- Verify icon sizes and resolutions
- Clear cache and rebuild

### Colors Not Applied
- Check CSS syntax and selectors
- Verify theme JSON structure
- Restart application after changes

### Build Errors
- Check TypeScript compilation
- Verify file imports and exports
- Review console for specific errors

## Next Steps

After branding is complete:
1. **Configure Build System** - Set up packaging and distribution
2. **Integrate Backend API** - Connect to Laravel backend
3. **Build Chat Panel** - Add GOC Agent AI interface
4. **Create Extensions** - Develop GOC-specific functionality
5. **Package for Distribution** - Create installers

## Resources

- [VS Code Theming Guide](https://code.visualstudio.com/api/extension-guides/color-theme)
- [Electron Icon Guidelines](https://www.electronjs.org/docs/latest/tutorial/application-distribution)
- [Icon Design Best Practices](https://developer.apple.com/design/human-interface-guidelines/app-icons)
- [Color Accessibility Guidelines](https://webaim.org/articles/contrast/)
