<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class WebResearchService
{
    protected string $googleApiKey;
    protected string $googleSearchEngineId;

    public function __construct()
    {
        $this->googleApiKey = env('GOOGLE_SEARCH_API_KEY', '');
        $this->googleSearchEngineId = env('GOOGLE_SEARCH_ENGINE_ID', '');
    }

    /**
     * Search the web using Google Custom Search API.
     */
    public function search(string $query, int $numResults = 5): array
    {
        try {
            if (empty($this->googleApiKey) || empty($this->googleSearchEngineId)) {
                return $this->fallbackSearch($query, $numResults);
            }

            // Check cache first
            $cacheKey = "web_search:" . md5($query . $numResults);
            $cached = Cache::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            $response = Http::timeout(30)->get('https://www.googleapis.com/customsearch/v1', [
                'key' => $this->googleApiKey,
                'cx' => $this->googleSearchEngineId,
                'q' => $query,
                'num' => min($numResults, 10),
                'safe' => 'active',
            ]);

            if (!$response->successful()) {
                Log::warning('Google Search API failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return $this->fallbackSearch($query, $numResults);
            }

            $data = $response->json();
            $results = [];

            if (isset($data['items'])) {
                foreach ($data['items'] as $item) {
                    $results[] = [
                        'title' => $item['title'] ?? '',
                        'url' => $item['link'] ?? '',
                        'snippet' => $item['snippet'] ?? '',
                        'display_url' => $item['displayLink'] ?? '',
                    ];
                }
            }

            $searchResult = [
                'success' => true,
                'query' => $query,
                'results' => $results,
                'total_results' => count($results),
                'search_time' => $data['searchInformation']['searchTime'] ?? 0,
                'provider' => 'google',
            ];

            // Cache for 1 hour
            Cache::put($cacheKey, $searchResult, 3600);

            return $searchResult;
        } catch (\Exception $e) {
            Log::error('Web search error', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'query' => $query,
                'results' => [],
            ];
        }
    }

    /**
     * Fallback search using DuckDuckGo (simplified).
     */
    protected function fallbackSearch(string $query, int $numResults): array
    {
        try {
            // This is a simplified implementation
            // In a real scenario, you'd use DuckDuckGo's API or scraping
            $results = [
                [
                    'title' => 'Search results for: ' . $query,
                    'url' => 'https://duckduckgo.com/?q=' . urlencode($query),
                    'snippet' => 'Fallback search - Google API not configured',
                    'display_url' => 'duckduckgo.com',
                ]
            ];

            return [
                'success' => true,
                'query' => $query,
                'results' => $results,
                'total_results' => count($results),
                'provider' => 'fallback',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'query' => $query,
                'results' => [],
            ];
        }
    }

    /**
     * Fetch and convert webpage content to markdown.
     */
    public function fetchContent(string $url): array
    {
        try {
            // Check cache first
            $cacheKey = "web_content:" . md5($url);
            $cached = Cache::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            $response = Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'GOC-Agent/1.0 (Web Research Bot)',
                ])
                ->get($url);

            if (!$response->successful()) {
                return [
                    'success' => false,
                    'error' => "Failed to fetch content: HTTP {$response->status()}",
                    'url' => $url,
                ];
            }

            $html = $response->body();
            $markdown = $this->convertHtmlToMarkdown($html);

            $result = [
                'success' => true,
                'url' => $url,
                'content' => $markdown,
                'length' => strlen($markdown),
                'title' => $this->extractTitle($html),
                'fetched_at' => now()->toISOString(),
            ];

            // Cache for 30 minutes
            Cache::put($cacheKey, $result, 1800);

            return $result;
        } catch (\Exception $e) {
            Log::error('Web content fetch error', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'url' => $url,
            ];
        }
    }

    /**
     * Convert HTML to Markdown (simplified implementation).
     */
    protected function convertHtmlToMarkdown(string $html): string
    {
        // Remove script and style tags
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Convert common HTML tags to Markdown
        $conversions = [
            '/<h1[^>]*>(.*?)<\/h1>/i' => '# $1',
            '/<h2[^>]*>(.*?)<\/h2>/i' => '## $1',
            '/<h3[^>]*>(.*?)<\/h3>/i' => '### $1',
            '/<h4[^>]*>(.*?)<\/h4>/i' => '#### $1',
            '/<h5[^>]*>(.*?)<\/h5>/i' => '##### $1',
            '/<h6[^>]*>(.*?)<\/h6>/i' => '###### $1',
            '/<strong[^>]*>(.*?)<\/strong>/i' => '**$1**',
            '/<b[^>]*>(.*?)<\/b>/i' => '**$1**',
            '/<em[^>]*>(.*?)<\/em>/i' => '*$1*',
            '/<i[^>]*>(.*?)<\/i>/i' => '*$1*',
            '/<code[^>]*>(.*?)<\/code>/i' => '`$1`',
            '/<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)<\/a>/i' => '[$2]($1)',
            '/<p[^>]*>(.*?)<\/p>/i' => '$1' . "\n\n",
            '/<br[^>]*>/i' => "\n",
            '/<li[^>]*>(.*?)<\/li>/i' => '- $1' . "\n",
        ];

        foreach ($conversions as $pattern => $replacement) {
            $html = preg_replace($pattern, $replacement, $html);
        }

        // Remove remaining HTML tags
        $text = strip_tags($html);

        // Clean up whitespace
        $text = preg_replace('/\n\s*\n\s*\n/', "\n\n", $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Extract title from HTML.
     */
    protected function extractTitle(string $html): string
    {
        if (preg_match('/<title[^>]*>(.*?)<\/title>/i', $html, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return 'Untitled';
    }

    /**
     * Summarize content using AI.
     */
    public function summarizeContent(string $content, int $maxLength = 500): array
    {
        try {
            // For now, return a simple truncated version
            // In a real implementation, you'd use an AI service for summarization
            $summary = strlen($content) > $maxLength 
                ? substr($content, 0, $maxLength) . '...'
                : $content;

            return [
                'success' => true,
                'original_length' => strlen($content),
                'summary_length' => strlen($summary),
                'summary' => $summary,
                'compression_ratio' => strlen($summary) / strlen($content),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Extract key information from content.
     */
    public function extractKeyInfo(string $content): array
    {
        try {
            $info = [
                'word_count' => str_word_count($content),
                'character_count' => strlen($content),
                'line_count' => substr_count($content, "\n") + 1,
                'links' => [],
                'headings' => [],
                'code_blocks' => [],
            ];

            // Extract links
            if (preg_match_all('/\[([^\]]+)\]\(([^)]+)\)/', $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $info['links'][] = [
                        'text' => $match[1],
                        'url' => $match[2],
                    ];
                }
            }

            // Extract headings
            if (preg_match_all('/^(#{1,6})\s+(.+)$/m', $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $info['headings'][] = [
                        'level' => strlen($match[1]),
                        'text' => $match[2],
                    ];
                }
            }

            // Extract code blocks
            if (preg_match_all('/```([^`]+)```/', $content, $matches)) {
                $info['code_blocks'] = $matches[1];
            }

            return [
                'success' => true,
                'data' => $info,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
