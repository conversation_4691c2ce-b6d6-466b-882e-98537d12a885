#!/usr/bin/env bash
# Verify that Microsoft branding and telemetry have been successfully removed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
PASSED=0
FAILED=0

# Function to print test results
print_test() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC} $test_name"
        [ -n "$details" ] && echo -e "  ${BLUE}→${NC} $details"
        ((PASSED++))
    else
        echo -e "${RED}✗ FAIL${NC} $test_name"
        [ -n "$details" ] && echo -e "  ${RED}→${NC} $details"
        ((FAILED++))
    fi
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

echo -e "${BLUE}🔍 GOC IDE Branding Verification${NC}\n"

# Check if we're in the right directory
if [ ! -f "product.json" ]; then
    echo -e "${RED}Error: product.json not found. Run this script in the VS Code source directory.${NC}"
    exit 1
fi

print_header "Product Configuration Tests"

# Test 1: Check product.json for GOC IDE branding
if grep -q "GOC IDE" product.json; then
    print_test "Product name updated to GOC IDE" "PASS"
else
    print_test "Product name updated to GOC IDE" "FAIL" "Still contains Microsoft branding"
fi

# Test 2: Check application name
if grep -q '"applicationName": "goc-ide"' product.json; then
    print_test "Application name set to goc-ide" "PASS"
else
    print_test "Application name set to goc-ide" "FAIL" "Application name not updated"
fi

# Test 3: Check data folder name
if grep -q '"dataFolderName": ".goc-ide"' product.json; then
    print_test "Data folder name set to .goc-ide" "PASS"
else
    print_test "Data folder name set to .goc-ide" "FAIL" "Still using .vscode folder"
fi

print_header "Telemetry Removal Tests"

# Test 4: Check for Microsoft telemetry URLs
if ! grep -r "vortex\.data\.microsoft\.com" . --exclude-dir=.git --exclude="*.backup" >/dev/null 2>&1; then
    print_test "Vortex telemetry endpoints removed" "PASS"
else
    print_test "Vortex telemetry endpoints removed" "FAIL" "Found remaining vortex.data.microsoft.com references"
fi

# Test 5: Check for mobile telemetry URLs
if ! grep -r "mobile\.events\.data\.microsoft\.com" . --exclude-dir=.git --exclude="*.backup" >/dev/null 2>&1; then
    print_test "Mobile telemetry endpoints removed" "PASS"
else
    print_test "Mobile telemetry endpoints removed" "FAIL" "Found remaining mobile.events.data.microsoft.com references"
fi

# Test 6: Check for general Microsoft data endpoints
MICROSOFT_DATA_COUNT=$(grep -r "\.data\.microsoft\.com" . --exclude-dir=.git --exclude="*.backup" 2>/dev/null | wc -l)
if [ "$MICROSOFT_DATA_COUNT" -eq 0 ]; then
    print_test "All Microsoft data endpoints removed" "PASS"
else
    print_test "All Microsoft data endpoints removed" "FAIL" "Found $MICROSOFT_DATA_COUNT remaining references"
fi

print_header "Extension Marketplace Tests"

# Test 7: Check for Open VSX marketplace
if grep -q "open-vsx.org" product.json; then
    print_test "Extension marketplace switched to Open VSX" "PASS"
else
    print_test "Extension marketplace switched to Open VSX" "FAIL" "Still using Microsoft marketplace"
fi

# Test 8: Check for Microsoft marketplace references
if ! grep -q "marketplace\.visualstudio\.com" product.json; then
    print_test "Microsoft marketplace references removed" "PASS"
else
    print_test "Microsoft marketplace references removed" "FAIL" "Still contains Microsoft marketplace URLs"
fi

print_header "Branding Tests"

# Test 9: Check for Microsoft branding in product.json
if ! grep -i "microsoft\|visual studio" product.json >/dev/null 2>&1; then
    print_test "Microsoft branding removed from product.json" "PASS"
else
    print_test "Microsoft branding removed from product.json" "FAIL" "Still contains Microsoft/Visual Studio references"
fi

# Test 10: Check for GOC Agent configuration
if grep -q "gocAgent" product.json; then
    print_test "GOC Agent configuration added" "PASS"
else
    print_test "GOC Agent configuration added" "FAIL" "GOC Agent configuration section missing"
fi

print_header "File System Tests"

# Test 11: Check if backup was created
if [ -f "product.json.backup" ]; then
    print_test "Backup file created" "PASS" "Original product.json backed up"
else
    print_test "Backup file created" "FAIL" "No backup found - risky!"
fi

# Test 12: Check for GOC product template
if [ -f "goc-product.json" ]; then
    print_test "GOC product template exists" "PASS"
else
    print_test "GOC product template exists" "FAIL" "goc-product.json template missing"
fi

print_header "Build System Tests"

# Test 13: Check if package.json exists
if [ -f "package.json" ]; then
    print_test "Package.json exists" "PASS"
else
    print_test "Package.json exists" "FAIL" "package.json missing"
fi

# Test 14: Check if node_modules exists (dependencies installed)
if [ -d "node_modules" ]; then
    print_test "Dependencies installed" "PASS"
else
    print_test "Dependencies installed" "FAIL" "Run 'npm install' to install dependencies"
fi

print_header "Summary"

TOTAL=$((PASSED + FAILED))
PERCENTAGE=$((PASSED * 100 / TOTAL))

echo -e "\n${BLUE}Test Results:${NC}"
echo -e "  ${GREEN}Passed: $PASSED${NC}"
echo -e "  ${RED}Failed: $FAILED${NC}"
echo -e "  ${BLUE}Total:  $TOTAL${NC}"
echo -e "  ${YELLOW}Success Rate: $PERCENTAGE%${NC}"

if [ $FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! GOC IDE branding removal successful.${NC}"
    echo -e "${GREEN}✓ Ready to proceed with GOC Agent integration.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please review and fix the issues above.${NC}"
    echo -e "${YELLOW}💡 Tip: Run './remove-branding.sh' again if needed.${NC}"
    exit 1
fi
