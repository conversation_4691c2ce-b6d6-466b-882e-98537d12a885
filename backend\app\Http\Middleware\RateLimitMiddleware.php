<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $next($request);
        }

        // Check daily quota
        $dailyKey = "rate_limit:daily:user:{$user->id}";
        $dailyCount = Cache::get($dailyKey, 0);

        if ($dailyCount >= $user->api_quota_daily) {
            return response()->json([
                'error' => 'Daily API quota exceeded',
                'quota' => $user->api_quota_daily,
                'used' => $dailyCount,
                'reset_at' => now()->endOfDay()
            ], 429);
        }

        // Check hourly rate limit (100 requests per hour)
        $hourlyKey = "rate_limit:hourly:user:{$user->id}";
        $hourlyCount = Cache::get($hourlyKey, 0);
        $hourlyLimit = 100;

        if ($hourlyCount >= $hourlyLimit) {
            return response()->json([
                'error' => 'Hourly rate limit exceeded',
                'limit' => $hourlyLimit,
                'used' => $hourlyCount,
                'reset_at' => now()->endOfHour()
            ], 429);
        }

        // Increment counters
        Cache::put($dailyKey, $dailyCount + 1, now()->endOfDay());
        Cache::put($hourlyKey, $hourlyCount + 1, now()->endOfHour());

        $response = $next($request);

        // Add rate limit headers
        $response->headers->set('X-RateLimit-Daily-Limit', $user->api_quota_daily);
        $response->headers->set('X-RateLimit-Daily-Remaining', max(0, $user->api_quota_daily - $dailyCount - 1));
        $response->headers->set('X-RateLimit-Hourly-Limit', $hourlyLimit);
        $response->headers->set('X-RateLimit-Hourly-Remaining', max(0, $hourlyLimit - $hourlyCount - 1));

        return $response;
    }
}
