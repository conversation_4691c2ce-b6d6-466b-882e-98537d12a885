#!/usr/bin/env bash
# Apply GOC Agent branding to VS Code fork

set -ex

echo "🎨 Applying GOC Agent branding..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "product.json" ]; then
    echo -e "${RED}Error: product.json not found. Make sure you're in the VS Code source directory.${NC}"
    exit 1
fi

print_status "Starting GOC Agent branding application..."

# 1. Create branding directories
print_status "Creating branding directories..."
mkdir -p goc-branding/{icons,splash,themes,assets}
mkdir -p goc-branding/icons/{win32,linux,darwin,web}

# 2. Update application titles and names
print_status "Updating application titles..."

# Update window titles
find src -name "*.ts" -type f | xargs grep -l "Visual Studio Code" | while read -r file; do
    sed -i 's/Visual Studio Code/GOC IDE/g' "$file"
    print_info "Updated title in $file"
done

# Update product names in TypeScript files
find src -name "*.ts" -type f | xargs grep -l "Code - OSS" | while read -r file; do
    sed -i 's/Code - OSS/GOC IDE/g' "$file"
    print_info "Updated product name in $file"
done

# 3. Update welcome screen
print_status "Customizing welcome screen..."

# Create custom welcome screen content
cat > src/vs/workbench/contrib/welcome/page/browser/gocWelcome.ts << 'EOF'
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) GOC Agent Team. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

export const gocWelcomeContent = {
    title: 'Welcome to GOC IDE',
    subtitle: 'AI-Powered Development Environment',
    description: 'Start coding with intelligent assistance from GOC Agent',
    features: [
        {
            title: 'AI Code Assistance',
            description: 'Get intelligent code suggestions and explanations',
            icon: 'robot'
        },
        {
            title: 'Smart Context Awareness',
            description: 'GOC Agent understands your entire codebase',
            icon: 'search'
        },
        {
            title: 'Integrated Chat',
            description: 'Ask questions and get help directly in the editor',
            icon: 'comment'
        },
        {
            title: 'Multi-Language Support',
            description: 'Works with PHP, JavaScript, Python, and more',
            icon: 'code'
        }
    ],
    quickActions: [
        {
            title: 'Open Folder',
            command: 'vscode.openFolder',
            icon: 'folder-opened'
        },
        {
            title: 'Clone Repository',
            command: 'git.clone',
            icon: 'repo-clone'
        },
        {
            title: 'New File',
            command: 'workbench.action.files.newUntitledFile',
            icon: 'new-file'
        },
        {
            title: 'GOC Agent Chat',
            command: 'goc.openChat',
            icon: 'comment-discussion'
        }
    ]
};
EOF

# 4. Update splash screen
print_status "Creating custom splash screen..."

cat > src/vs/workbench/browser/parts/splash/gocSplash.ts << 'EOF'
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) GOC Agent Team. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

export const gocSplashConfig = {
    title: 'GOC IDE',
    subtitle: 'Loading AI-Powered Development Environment...',
    loadingMessages: [
        'Initializing GOC Agent...',
        'Loading intelligent features...',
        'Preparing code assistance...',
        'Setting up workspace...',
        'Almost ready...'
    ],
    colors: {
        primary: '#007ACC',
        secondary: '#1E1E1E',
        accent: '#00D4AA',
        text: '#FFFFFF'
    }
};
EOF

# 5. Create custom CSS for branding
print_status "Creating custom CSS..."

cat > goc-branding/goc-theme.css << 'EOF'
/* GOC IDE Custom Branding Styles */

/* Welcome Screen Customization */
.welcome-page .title {
    color: #007ACC;
    font-weight: 600;
}

.welcome-page .subtitle {
    color: #00D4AA;
    font-size: 18px;
    margin-bottom: 20px;
}

.goc-feature-card {
    background: linear-gradient(135deg, #007ACC 0%, #00D4AA 100%);
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    color: white;
    transition: transform 0.2s ease;
}

.goc-feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 116, 204, 0.3);
}

/* Splash Screen Customization */
.splash-screen {
    background: linear-gradient(135deg, #1E1E1E 0%, #2D2D30 100%);
}

.splash-title {
    color: #007ACC;
    font-size: 32px;
    font-weight: 300;
    margin-bottom: 10px;
}

.splash-subtitle {
    color: #00D4AA;
    font-size: 16px;
    opacity: 0.8;
}

/* Activity Bar Customization */
.monaco-workbench .activitybar {
    background: linear-gradient(180deg, #007ACC 0%, #005a9e 100%);
}

/* Status Bar Customization */
.monaco-workbench .statusbar {
    background: linear-gradient(90deg, #007ACC 0%, #00D4AA 100%);
}

/* Title Bar Customization */
.monaco-workbench .titlebar {
    background: #1E1E1E;
    border-bottom: 2px solid #007ACC;
}

/* GOC Agent Chat Panel */
.goc-chat-panel {
    background: #252526;
    border-left: 3px solid #007ACC;
}

.goc-chat-header {
    background: linear-gradient(90deg, #007ACC 0%, #00D4AA 100%);
    color: white;
    padding: 12px;
    font-weight: 600;
}

.goc-chat-input {
    border: 1px solid #007ACC;
    border-radius: 4px;
    background: #1E1E1E;
    color: #CCCCCC;
}

.goc-chat-input:focus {
    border-color: #00D4AA;
    box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

/* Command Palette Customization */
.quick-input-widget {
    border: 2px solid #007ACC;
}

/* Editor Customization */
.monaco-editor .margin {
    background: #1E1E1E;
}

/* Minimap Customization */
.monaco-editor .minimap {
    border-left: 1px solid #007ACC;
}

/* Scrollbar Customization */
.monaco-scrollable-element > .scrollbar > .slider {
    background: rgba(0, 116, 204, 0.6);
}

.monaco-scrollable-element > .scrollbar > .slider:hover {
    background: rgba(0, 116, 204, 0.8);
}

/* Button Customization */
.monaco-button.primary {
    background: linear-gradient(135deg, #007ACC 0%, #00D4AA 100%);
    border: none;
}

.monaco-button.primary:hover {
    background: linear-gradient(135deg, #005a9e 0%, #00b894 100%);
}

/* Tab Customization */
.monaco-workbench .part.editor > .content .editor-group-container > .title .tabs-container > .tab.active {
    border-top: 2px solid #007ACC;
}

/* Notification Customization */
.monaco-workbench .notifications-list-container .notification-list-item {
    border-left: 4px solid #007ACC;
}

/* Progress Bar Customization */
.monaco-progress-container .progress-bit {
    background: linear-gradient(90deg, #007ACC 0%, #00D4AA 100%);
}
EOF

# 6. Update package.json with GOC branding
print_status "Updating package.json..."

if [ -f "package.json" ]; then
    # Update display name and description
    sed -i 's/"displayName": ".*"/"displayName": "GOC IDE"/' package.json
    sed -i 's/"description": ".*"/"description": "AI-Powered Development Environment with GOC Agent Integration"/' package.json
    
    # Update repository URL (placeholder)
    sed -i 's|"url": ".*"|"url": "https://github.com/YOUR_USERNAME/goc-ide.git"|' package.json
    
    print_status "package.json updated"
fi

# 7. Create icon placeholders and instructions
print_status "Creating icon placeholders..."

cat > goc-branding/ICON_REQUIREMENTS.md << 'EOF'
# GOC IDE Icon Requirements

## Required Icon Files

### Windows Icons (.ico format)
- `resources/win32/code.ico` - Main application icon (256x256, 128x128, 64x64, 48x48, 32x32, 16x16)
- `resources/win32/code_70x70.png` - Tile icon for Windows Store
- `resources/win32/code_150x150.png` - Large tile icon

### macOS Icons (.icns format)
- `resources/darwin/code.icns` - Main application icon (1024x1024, 512x512, 256x256, 128x128, 64x64, 32x32, 16x16)

### Linux Icons (.png format)
- `resources/linux/code.png` - Main application icon (512x512)
- `resources/linux/code-16.png` - Small icon (16x16)
- `resources/linux/code-24.png` - Medium icon (24x24)
- `resources/linux/code-32.png` - Large icon (32x32)
- `resources/linux/code-48.png` - Extra large icon (48x48)
- `resources/linux/code-64.png` - Huge icon (64x64)
- `resources/linux/code-128.png` - Giant icon (128x128)
- `resources/linux/code-256.png` - Massive icon (256x256)
- `resources/linux/code-512.png` - Ultra icon (512x512)

### Web Icons (.ico, .png format)
- `resources/server/code-web.ico` - Web application icon
- `resources/server/favicon.ico` - Browser favicon

## Design Guidelines

### Color Scheme
- Primary: #007ACC (GOC Blue)
- Secondary: #00D4AA (GOC Green)
- Background: #1E1E1E (Dark)
- Text: #FFFFFF (White)

### Icon Style
- Modern, clean design
- Recognizable at small sizes
- Consistent with GOC Agent branding
- Professional appearance
- Good contrast for visibility

### Recommended Tools
- Adobe Illustrator/Photoshop
- GIMP (free alternative)
- Inkscape (free vector editor)
- Online icon generators

## Installation
After creating icons, copy them to the appropriate directories:
```bash
cp goc-branding/icons/win32/* resources/win32/
cp goc-branding/icons/darwin/* resources/darwin/
cp goc-branding/icons/linux/* resources/linux/
cp goc-branding/icons/web/* resources/server/
```
EOF

# 8. Create theme configuration
print_status "Creating theme configuration..."

cat > goc-branding/goc-theme.json << 'EOF'
{
  "name": "GOC Agent Dark",
  "type": "dark",
  "colors": {
    "activityBar.background": "#007ACC",
    "activityBar.foreground": "#FFFFFF",
    "activityBarBadge.background": "#00D4AA",
    "activityBarBadge.foreground": "#000000",
    "statusBar.background": "#007ACC",
    "statusBar.foreground": "#FFFFFF",
    "statusBar.noFolderBackground": "#005a9e",
    "statusBarItem.hoverBackground": "#00D4AA",
    "titleBar.activeBackground": "#1E1E1E",
    "titleBar.activeForeground": "#CCCCCC",
    "titleBar.border": "#007ACC",
    "editor.background": "#1E1E1E",
    "editor.foreground": "#D4D4D4",
    "editorCursor.foreground": "#00D4AA",
    "editor.selectionBackground": "#264F78",
    "editor.lineHighlightBackground": "#2A2D2E",
    "editorLineNumber.foreground": "#858585",
    "editorLineNumber.activeForeground": "#007ACC",
    "sideBar.background": "#252526",
    "sideBar.foreground": "#CCCCCC",
    "sideBarTitle.foreground": "#007ACC",
    "panel.background": "#1E1E1E",
    "panel.border": "#007ACC",
    "panelTitle.activeForeground": "#00D4AA",
    "terminal.background": "#1E1E1E",
    "terminal.foreground": "#D4D4D4",
    "button.background": "#007ACC",
    "button.hoverBackground": "#005a9e",
    "input.background": "#3C3C3C",
    "input.border": "#007ACC",
    "inputOption.activeBorder": "#00D4AA",
    "dropdown.background": "#3C3C3C",
    "dropdown.border": "#007ACC",
    "list.activeSelectionBackground": "#007ACC",
    "list.hoverBackground": "#2A2D2E",
    "progressBar.background": "#00D4AA"
  },
  "tokenColors": [
    {
      "scope": "comment",
      "settings": {
        "foreground": "#6A9955",
        "fontStyle": "italic"
      }
    },
    {
      "scope": "keyword",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "string",
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": "variable",
      "settings": {
        "foreground": "#9CDCFE"
      }
    },
    {
      "scope": "function",
      "settings": {
        "foreground": "#DCDCAA"
      }
    }
  ]
}
EOF

print_status "GOC Agent branding application complete!"
print_warning "Next steps:"
print_warning "1. Create actual icon files (see goc-branding/ICON_REQUIREMENTS.md)"
print_warning "2. Copy icons to resources/ directories"
print_warning "3. Test the build to ensure branding appears correctly"
print_warning "4. Customize colors and styles in goc-branding/goc-theme.css"

echo -e "${GREEN}🎉 GOC Agent branding setup completed successfully!${NC}"
echo -e "${BLUE}📁 Branding files created in goc-branding/ directory${NC}"
