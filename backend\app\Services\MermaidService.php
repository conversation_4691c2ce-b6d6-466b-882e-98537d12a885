<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MermaidService
{
    protected string $cachePrefix = 'mermaid:';
    protected int $cacheTtl = 3600; // 1 hour

    /**
     * Render a Mermaid diagram.
     */
    public function renderDiagram(string $diagramDefinition, string $title = 'Mermaid Diagram', array $options = []): array
    {
        try {
            // Validate diagram definition
            $validationResult = $this->validateDiagram($diagramDefinition);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'error' => $validationResult['error'],
                ];
            }

            // Generate unique ID for this diagram
            $diagramId = $this->generateDiagramId($diagramDefinition);
            
            // Check cache first
            $cacheKey = $this->cachePrefix . $diagramId;
            $cached = Cache::get($cacheKey);
            if ($cached) {
                return [
                    'success' => true,
                    'data' => $cached,
                    'from_cache' => true,
                ];
            }

            // Generate HTML for the diagram
            $html = $this->generateDiagramHtml($diagramDefinition, $title, $options);
            
            // Save to storage
            $filename = "diagrams/{$diagramId}.html";
            Storage::disk('public')->put($filename, $html);
            
            $result = [
                'diagram_id' => $diagramId,
                'title' => $title,
                'definition' => $diagramDefinition,
                'html_content' => $html,
                'file_path' => $filename,
                'url' => Storage::disk('public')->url($filename),
                'created_at' => now()->toISOString(),
                'options' => $options,
            ];

            // Cache the result
            Cache::put($cacheKey, $result, $this->cacheTtl);

            return [
                'success' => true,
                'data' => $result,
                'from_cache' => false,
            ];
        } catch (\Exception $e) {
            Log::error("Failed to render Mermaid diagram: {$e->getMessage()}");
            
            return [
                'success' => false,
                'error' => "Failed to render diagram: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Get a previously rendered diagram.
     */
    public function getDiagram(string $diagramId): array
    {
        try {
            $cacheKey = $this->cachePrefix . $diagramId;
            $cached = Cache::get($cacheKey);
            
            if ($cached) {
                return [
                    'success' => true,
                    'data' => $cached,
                ];
            }

            // Try to load from storage
            $filename = "diagrams/{$diagramId}.html";
            if (Storage::disk('public')->exists($filename)) {
                $html = Storage::disk('public')->get($filename);
                
                $result = [
                    'diagram_id' => $diagramId,
                    'html_content' => $html,
                    'file_path' => $filename,
                    'url' => Storage::disk('public')->url($filename),
                ];
                
                return [
                    'success' => true,
                    'data' => $result,
                ];
            }

            return [
                'success' => false,
                'error' => 'Diagram not found',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to get diagram: {$e->getMessage()}",
            ];
        }
    }

    /**
     * List all rendered diagrams.
     */
    public function listDiagrams(): array
    {
        try {
            $files = Storage::disk('public')->files('diagrams');
            $diagrams = [];
            
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'html') {
                    $diagramId = pathinfo($file, PATHINFO_FILENAME);
                    $cacheKey = $this->cachePrefix . $diagramId;
                    $cached = Cache::get($cacheKey);
                    
                    if ($cached) {
                        $diagrams[] = [
                            'diagram_id' => $diagramId,
                            'title' => $cached['title'] ?? 'Untitled',
                            'created_at' => $cached['created_at'] ?? null,
                            'url' => $cached['url'],
                        ];
                    } else {
                        $diagrams[] = [
                            'diagram_id' => $diagramId,
                            'title' => 'Untitled',
                            'created_at' => null,
                            'url' => Storage::disk('public')->url($file),
                        ];
                    }
                }
            }

            return [
                'success' => true,
                'data' => [
                    'diagrams' => $diagrams,
                    'total_count' => count($diagrams),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to list diagrams: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Delete a diagram.
     */
    public function deleteDiagram(string $diagramId): array
    {
        try {
            // Remove from cache
            $cacheKey = $this->cachePrefix . $diagramId;
            Cache::forget($cacheKey);
            
            // Remove from storage
            $filename = "diagrams/{$diagramId}.html";
            if (Storage::disk('public')->exists($filename)) {
                Storage::disk('public')->delete($filename);
            }

            return [
                'success' => true,
                'message' => 'Diagram deleted successfully',
                'diagram_id' => $diagramId,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to delete diagram: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Validate Mermaid diagram definition.
     */
    protected function validateDiagram(string $definition): array
    {
        $definition = trim($definition);
        
        if (empty($definition)) {
            return [
                'valid' => false,
                'error' => 'Diagram definition cannot be empty',
            ];
        }

        // Check for basic Mermaid syntax patterns
        $validPatterns = [
            'graph',
            'flowchart',
            'sequenceDiagram',
            'classDiagram',
            'stateDiagram',
            'erDiagram',
            'journey',
            'gantt',
            'pie',
            'gitgraph',
        ];

        $hasValidPattern = false;
        foreach ($validPatterns as $pattern) {
            if (stripos($definition, $pattern) !== false) {
                $hasValidPattern = true;
                break;
            }
        }

        if (!$hasValidPattern) {
            return [
                'valid' => false,
                'error' => 'Invalid Mermaid diagram type. Must start with a valid diagram type.',
            ];
        }

        return [
            'valid' => true,
        ];
    }

    /**
     * Generate unique diagram ID.
     */
    protected function generateDiagramId(string $definition): string
    {
        return 'mermaid_' . md5($definition) . '_' . time();
    }

    /**
     * Generate HTML for Mermaid diagram.
     */
    protected function generateDiagramHtml(string $definition, string $title, array $options): string
    {
        $theme = $options['theme'] ?? 'default';
        $width = $options['width'] ?? '100%';
        $height = $options['height'] ?? 'auto';
        
        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title}</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: #4f46e5;
            color: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e5e7eb;
        }
        .header h1 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }
        .content {
            padding: 24px;
        }
        .diagram-container {
            width: {$width};
            height: {$height};
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
        }
        .controls {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        .btn-primary {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }
        .btn-primary:hover {
            background: #4338ca;
        }
        .footer {
            padding: 16px 24px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{$title}</h1>
        </div>
        <div class="content">
            <div class="diagram-container">
                <div class="mermaid" id="diagram">
{$definition}
                </div>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="downloadSVG()">Download SVG</button>
                <button class="btn" onclick="downloadPNG()">Download PNG</button>
                <button class="btn" onclick="copyToClipboard()">Copy Definition</button>
                <button class="btn" onclick="toggleFullscreen()">Fullscreen</button>
            </div>
        </div>
        <div class="footer">
            Generated by GOC Agent • Powered by Mermaid.js
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: '{$theme}',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        function downloadSVG() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const blob = new Blob([svgData], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = '{$title}.svg';
                a.click();
                URL.revokeObjectURL(url);
            }
        }

        function downloadPNG() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                const svgData = new XMLSerializer().serializeToString(svg);
                const blob = new Blob([svgData], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    canvas.toBlob(function(blob) {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = '{$title}.png';
                        a.click();
                        URL.revokeObjectURL(url);
                    });
                };
                img.src = url;
            }
        }

        function copyToClipboard() {
            const definition = `{$definition}`;
            navigator.clipboard.writeText(definition).then(() => {
                alert('Diagram definition copied to clipboard!');
            });
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
    </script>
</body>
</html>
HTML;
    }
}
