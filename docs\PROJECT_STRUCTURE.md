# GOC Agent Project Structure

This document outlines the clean, simple structure of the GOC Agent ecosystem.

## 📁 Root Directory Structure

```
goc-agent/
├── goc-agent-cli/               # CLI Source Code & Dependencies
├── backend-final/               # Laravel Backend (rename to backend/)
├── goc-ide/                     # GOC IDE Editor
├── goc-extension/               # VS Code Extension
├── docs/                        # All Documentation
├── package.json                 # Workspace Scripts Only
└── README.md                    # Main Project README
```

## 🎯 Key Principles

- **Independent Components**: Each directory has its own `package.json` and `node_modules`
- **Clean Separation**: No shared dependencies or complex workspace configurations
- **Simple Management**: Each component can be developed and deployed independently
- **Clear Naming**: Descriptive folder names that clearly indicate purpose

## 🖥️ CLI Component (`goc-agent-cli/`)

The core command-line interface and AI agent functionality.

**Key Features:**
- Multi-provider AI support (Ollama, OpenAI, Groq, Gemini)
- Self-learning and training capabilities
- Web browsing and research
- Codebase analysis and context building
- Technology-specific training

**Structure:**
```
goc-agent-cli/
├── agent/                        # Core agent logic
├── ai/                          # AI provider integrations
├── commands/                    # CLI command handlers
├── config/                      # Configuration management
├── context/                     # Context engine
├── training/                    # Learning and training
├── utils/                       # Utility functions
├── web/                         # Web browsing capabilities
├── services/                    # Backend API integration
├── cli.ts                       # Main CLI entry point
├── package.json                 # Independent dependencies
├── tsconfig.json                # TypeScript configuration
├── goc.js                       # Global CLI entry point
└── node_modules/                # CLI-specific dependencies
```

## 🌐 Backend Component (`backend-final/` → `backend/`)

Laravel-based web application providing API and web interface.

**Key Features:**
- User authentication and authorization
- Subscription and billing management
- API key management
- Session and context storage
- Admin dashboard
- Team collaboration

**Structure:**
```
backend/
├── app/                         # Laravel application
│   ├── Http/Controllers/        # API and web controllers
│   ├── Models/                  # Database models
│   ├── Services/                # Business logic services
│   └── Providers/               # Service providers
├── config/                      # Laravel configuration
├── database/                    # Migrations and seeders
├── resources/                   # Views and assets
├── routes/                      # API and web routes
├── public/                      # Public assets
├── package.json                 # Frontend dependencies
├── composer.json                # PHP dependencies
├── node_modules/                # Frontend dependencies
└── vendor/                      # PHP dependencies
```

## 📝 IDE Component (`goc-ide/`)

Electron-based VS Code-like editor with extension support.

**Key Features:**
- Monaco editor integration
- Extension system compatibility
- Integrated terminal
- File explorer
- GOC Agent integration
- Theme support

**Structure:**
```
goc-ide/
├── src/                         # Source code
│   ├── main/                    # Electron main process
│   ├── renderer/                # Renderer process (UI)
│   └── extensions/              # Extension host
├── build/                       # Build configuration
├── package.json                 # Independent dependencies
└── node_modules/                # IDE-specific dependencies
```

## 🔌 Extension Component (`goc-extension/`)

Official VS Code extension for GOC Agent integration.

**Key Features:**
- Direct CLI integration
- Context-aware assistance
- Code explanation and generation
- Training integration
- Web research capabilities

**Structure:**
```
goc-extension/
├── src/                         # TypeScript source
├── out/                         # Compiled JavaScript
├── package.json                 # Extension manifest
├── README.md                    # Extension documentation
└── node_modules/                # Extension dependencies
```

## 📚 Documentation (`docs/`)

Centralized documentation for the entire ecosystem.

**Contents:**
- Architecture documentation
- Feature comparisons
- Implementation guides
- API documentation
- User guides
- Development guides

## 🛠️ Development Workflow

### Building Components
```bash
# Build CLI
npm run build

# Build all components
npm run build:all

# Build specific components
npm run build:backend
npm run build:ide
npm run build:extension
```

### Development Servers
```bash
# CLI development
npm run dev

# Backend development
npm run dev:backend

# IDE development
npm run dev:ide
```

### Package Management
Each component has its own `package.json` and dependencies, managed through the workspace configuration in the root `package.json`.

## 🚀 Deployment

### CLI Distribution
- NPM package for global installation
- Binary distributions for different platforms

### Backend Deployment
- Laravel application deployment
- Database migrations
- Environment configuration

### IDE Distribution
- Electron app packaging
- Platform-specific installers

### Extension Distribution
- VS Code Marketplace
- Manual VSIX installation

## 🔧 Configuration

### Environment Files
- Root: General configuration
- Backend: Laravel environment
- IDE: Electron configuration
- Extension: VS Code settings

### Shared Configuration
Common settings are shared through the workspace configuration and can be overridden per component.
