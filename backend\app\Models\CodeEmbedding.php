<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CodeEmbedding extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'project_path',
        'file_path',
        'content_hash',
        'embedding_vector',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'embedding_vector' => 'array',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the user that owns the code embedding.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
