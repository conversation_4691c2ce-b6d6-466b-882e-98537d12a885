<?php

namespace App\Http\Middleware;

use App\Models\ApiUsage;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UsageTrackingMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);

        $response = $next($request);

        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000);

        // Only track for authenticated users
        if ($request->user()) {
            ApiUsage::create([
                'user_id' => $request->user()->id,
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'status_code' => $response->getStatusCode(),
                'response_time_ms' => $responseTime,
                'tokens_used' => $response->headers->get('X-Tokens-Used', 0),
                'provider' => $response->headers->get('X-Provider'),
                'model' => $response->headers->get('X-Model'),
            ]);
        }

        return $response;
    }
}
