{"nameShort": "GOC IDE", "nameLong": "GOC IDE - AI-Powered Code Editor", "applicationName": "goc-ide", "dataFolderName": ".goc-ide", "win32MutexName": "gocide", "licenseName": "MIT", "licenseUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/LICENSE", "win32DirName": "GOC IDE", "win32NameVersion": "GOC IDE", "win32RegValueName": "GocIde", "win32AppUserModelId": "GocAgent.GocIde", "win32ShellNameShort": "GOC IDE", "darwinBundleIdentifier": "com.goc-agent.ide", "linuxIconName": "goc-ide", "reportIssueUrl": "https://github.com/YOUR_USERNAME/goc-ide/issues/new", "urlProtocol": "goc-ide", "extensionAllowedBadgeProviders": ["api.bintray.com", "api.travis-ci.com", "api.travis-ci.org", "app.fossa.io", "badge.buildkite.com", "badge.fury.io", "badgen.net", "badges.frapsoft.com", "badges.gitter.im", "cdn.travis-ci.com", "cdn.travis-ci.org", "ci.appveyor.com", "circleci.com", "codacy.com", "codeclimate.com", "codecov.io", "coveralls.io", "david-dm.org", "deepscan.io", "dev.azure.com", "docs.rs", "flat.badgen.net", "gemnasium.com", "githost.io", "gitlab.com", "godoc.org", "goreportcard.com", "img.shields.io", "isitmaintained.com", "nodesecurity.io", "opencollective.com", "snyk.io", "travis-ci.com", "travis-ci.org", "www.bithound.io", "www.versioneye.com"], "extensionAllowedBadgeProvidersRegex": ["^https:\\/\\/github\\.com\\/[^/]+\\/[^/]+\\/(actions\\/)?workflows\\/.*badge\\.svg"], "linkProtectionTrustedDomains": ["https://open-vsx.org", "https://goc-agent.com"], "documentationUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/README.md", "keyboardShortcutsUrlMac": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/shortcuts-mac.md", "keyboardShortcutsUrlLinux": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/shortcuts-linux.md", "keyboardShortcutsUrlWin": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/shortcuts-windows.md", "introductoryVideosUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/videos.md", "tipsAndTricksUrl": "https://github.com/YOUR_USERNAME/goc-ide/blob/main/docs/tips.md", "twitterUrl": "https://twitter.com/goc_agent", "requestFeatureUrl": "https://github.com/YOUR_USERNAME/goc-ide/issues/new?template=feature_request.md", "extensionsGallery": {"serviceUrl": "https://open-vsx.org/vscode/gallery", "cacheUrl": "https://open-vsx.org/vscode/gallery", "itemUrl": "https://open-vsx.org/vscode/item", "controlUrl": "", "recommendationsUrl": ""}, "settingsSearchBuildId": "", "settingsSearchUrl": "", "tasConfig": {"endpoint": "", "telemetryEventName": "", "featuresTelemetryPropertyName": "", "assignmentContextTelemetryPropertyName": ""}, "experimentsUrl": "", "serverGreeting": [], "enableTelemetry": false, "aiConfig": {"ariaKey": ""}, "msftInternalDomains": [], "welcomePage": {"title": "Welcome to GOC IDE", "subtitle": "AI-Powered Development Environment", "description": "Start coding with intelligent assistance from GOC Agent"}, "gocAgent": {"enabled": true, "apiUrl": "http://localhost:8000/api", "chatEnabled": true, "autoComplete": true, "contextAware": true}}