<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class MemoryService
{
    protected string $cachePrefix = 'memory:';
    protected int $defaultTtl = 86400; // 24 hours

    /**
     * Store a memory for a user.
     */
    public function remember(User $user, string $memory, array $metadata = []): array
    {
        try {
            $memoryId = $this->generateMemoryId();
            $timestamp = now();
            
            $memoryData = [
                'id' => $memoryId,
                'user_id' => $user->id,
                'content' => $memory,
                'metadata' => $metadata,
                'created_at' => $timestamp->toISOString(),
                'tags' => $this->extractTags($memory),
                'importance' => $this->calculateImportance($memory, $metadata),
            ];

            // Store in cache with user-specific key
            $cacheKey = $this->getUserMemoryKey($user->id, $memoryId);
            Cache::put($cacheKey, $memoryData, $this->defaultTtl);

            // Add to user's memory index
            $this->addToMemoryIndex($user->id, $memoryId, $memoryData);

            Log::info("Memory stored for user {$user->id}: {$memoryId}");

            return [
                'success' => true,
                'data' => [
                    'memory_id' => $memoryId,
                    'content' => $memory,
                    'created_at' => $timestamp->toISOString(),
                    'tags' => $memoryData['tags'],
                    'importance' => $memoryData['importance'],
                ],
                'message' => 'Memory stored successfully',
            ];
        } catch (\Exception $e) {
            Log::error("Failed to store memory for user {$user->id}: {$e->getMessage()}");
            
            return [
                'success' => false,
                'error' => "Failed to store memory: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Retrieve memories for a user.
     */
    public function getMemories(User $user, array $filters = []): array
    {
        try {
            $memoryIndex = $this->getMemoryIndex($user->id);
            $memories = [];

            foreach ($memoryIndex as $memoryId => $indexData) {
                $cacheKey = $this->getUserMemoryKey($user->id, $memoryId);
                $memoryData = Cache::get($cacheKey);
                
                if ($memoryData && $this->matchesFilters($memoryData, $filters)) {
                    $memories[] = $memoryData;
                }
            }

            // Sort by importance and recency
            usort($memories, function ($a, $b) {
                $importanceDiff = $b['importance'] - $a['importance'];
                if ($importanceDiff !== 0) {
                    return $importanceDiff;
                }
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });

            return [
                'success' => true,
                'data' => [
                    'memories' => $memories,
                    'total_count' => count($memories),
                    'filters_applied' => $filters,
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to retrieve memories: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Search memories by content or tags.
     */
    public function searchMemories(User $user, string $query, int $limit = 10): array
    {
        try {
            $memoryIndex = $this->getMemoryIndex($user->id);
            $matches = [];

            foreach ($memoryIndex as $memoryId => $indexData) {
                $cacheKey = $this->getUserMemoryKey($user->id, $memoryId);
                $memoryData = Cache::get($cacheKey);
                
                if ($memoryData) {
                    $score = $this->calculateRelevanceScore($memoryData, $query);
                    if ($score > 0) {
                        $memoryData['relevance_score'] = $score;
                        $matches[] = $memoryData;
                    }
                }
            }

            // Sort by relevance score
            usort($matches, fn($a, $b) => $b['relevance_score'] - $a['relevance_score']);

            // Limit results
            $matches = array_slice($matches, 0, $limit);

            return [
                'success' => true,
                'data' => [
                    'memories' => $matches,
                    'query' => $query,
                    'total_matches' => count($matches),
                    'limit' => $limit,
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to search memories: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Delete a memory.
     */
    public function forgetMemory(User $user, string $memoryId): array
    {
        try {
            $cacheKey = $this->getUserMemoryKey($user->id, $memoryId);
            $memoryData = Cache::get($cacheKey);

            if (!$memoryData) {
                return [
                    'success' => false,
                    'error' => 'Memory not found',
                ];
            }

            // Remove from cache
            Cache::forget($cacheKey);

            // Remove from memory index
            $this->removeFromMemoryIndex($user->id, $memoryId);

            return [
                'success' => true,
                'message' => 'Memory deleted successfully',
                'deleted_memory_id' => $memoryId,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to delete memory: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Get memory statistics for a user.
     */
    public function getMemoryStats(User $user): array
    {
        try {
            $memoryIndex = $this->getMemoryIndex($user->id);
            $totalMemories = count($memoryIndex);
            $totalSize = 0;
            $tagCounts = [];
            $importanceDistribution = ['low' => 0, 'medium' => 0, 'high' => 0];

            foreach ($memoryIndex as $memoryId => $indexData) {
                $cacheKey = $this->getUserMemoryKey($user->id, $memoryId);
                $memoryData = Cache::get($cacheKey);
                
                if ($memoryData) {
                    $totalSize += strlen($memoryData['content']);
                    
                    // Count tags
                    foreach ($memoryData['tags'] as $tag) {
                        $tagCounts[$tag] = ($tagCounts[$tag] ?? 0) + 1;
                    }
                    
                    // Count importance levels
                    $importance = $memoryData['importance'];
                    if ($importance >= 0.7) {
                        $importanceDistribution['high']++;
                    } elseif ($importance >= 0.4) {
                        $importanceDistribution['medium']++;
                    } else {
                        $importanceDistribution['low']++;
                    }
                }
            }

            return [
                'success' => true,
                'data' => [
                    'total_memories' => $totalMemories,
                    'total_size_bytes' => $totalSize,
                    'average_size_bytes' => $totalMemories > 0 ? round($totalSize / $totalMemories) : 0,
                    'tag_counts' => $tagCounts,
                    'importance_distribution' => $importanceDistribution,
                    'most_common_tags' => array_slice(
                        array_keys(array_slice(arsort($tagCounts) ? $tagCounts : [], 0, 5, true)), 
                        0, 
                        5
                    ),
                ],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Failed to get memory stats: {$e->getMessage()}",
            ];
        }
    }

    /**
     * Generate a unique memory ID.
     */
    protected function generateMemoryId(): string
    {
        return 'mem_' . uniqid() . '_' . time();
    }

    /**
     * Get cache key for user memory.
     */
    protected function getUserMemoryKey(int $userId, string $memoryId): string
    {
        return $this->cachePrefix . "user:{$userId}:memory:{$memoryId}";
    }

    /**
     * Get cache key for user memory index.
     */
    protected function getUserMemoryIndexKey(int $userId): string
    {
        return $this->cachePrefix . "user:{$userId}:index";
    }

    /**
     * Extract tags from memory content.
     */
    protected function extractTags(string $content): array
    {
        $tags = [];
        
        // Extract hashtags
        if (preg_match_all('/#(\w+)/', $content, $matches)) {
            $tags = array_merge($tags, $matches[1]);
        }
        
        // Extract keywords based on content
        $keywords = ['bug', 'feature', 'todo', 'important', 'urgent', 'meeting', 'deadline', 'code', 'api', 'database'];
        foreach ($keywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                $tags[] = $keyword;
            }
        }
        
        return array_unique($tags);
    }

    /**
     * Calculate importance score for memory.
     */
    protected function calculateImportance(string $content, array $metadata): float
    {
        $score = 0.5; // Base score
        
        // Increase importance for certain keywords
        $importantKeywords = ['urgent', 'important', 'critical', 'deadline', 'bug', 'security'];
        foreach ($importantKeywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                $score += 0.2;
            }
        }
        
        // Increase importance for longer content
        if (strlen($content) > 100) {
            $score += 0.1;
        }
        
        // Check metadata for importance hints
        if (isset($metadata['priority']) && $metadata['priority'] === 'high') {
            $score += 0.3;
        }
        
        return min(1.0, $score);
    }

    /**
     * Add memory to user's memory index.
     */
    protected function addToMemoryIndex(int $userId, string $memoryId, array $memoryData): void
    {
        $indexKey = $this->getUserMemoryIndexKey($userId);
        $index = Cache::get($indexKey, []);
        
        $index[$memoryId] = [
            'created_at' => $memoryData['created_at'],
            'tags' => $memoryData['tags'],
            'importance' => $memoryData['importance'],
        ];
        
        Cache::put($indexKey, $index, $this->defaultTtl);
    }

    /**
     * Get user's memory index.
     */
    protected function getMemoryIndex(int $userId): array
    {
        $indexKey = $this->getUserMemoryIndexKey($userId);
        return Cache::get($indexKey, []);
    }

    /**
     * Remove memory from user's memory index.
     */
    protected function removeFromMemoryIndex(int $userId, string $memoryId): void
    {
        $indexKey = $this->getUserMemoryIndexKey($userId);
        $index = Cache::get($indexKey, []);
        
        unset($index[$memoryId]);
        
        Cache::put($indexKey, $index, $this->defaultTtl);
    }

    /**
     * Check if memory matches filters.
     */
    protected function matchesFilters(array $memoryData, array $filters): bool
    {
        if (isset($filters['tags']) && !empty($filters['tags'])) {
            $hasMatchingTag = false;
            foreach ($filters['tags'] as $filterTag) {
                if (in_array($filterTag, $memoryData['tags'])) {
                    $hasMatchingTag = true;
                    break;
                }
            }
            if (!$hasMatchingTag) {
                return false;
            }
        }
        
        if (isset($filters['min_importance']) && $memoryData['importance'] < $filters['min_importance']) {
            return false;
        }
        
        if (isset($filters['since']) && strtotime($memoryData['created_at']) < strtotime($filters['since'])) {
            return false;
        }
        
        return true;
    }

    /**
     * Calculate relevance score for search query.
     */
    protected function calculateRelevanceScore(array $memoryData, string $query): float
    {
        $score = 0;
        $queryLower = strtolower($query);
        $contentLower = strtolower($memoryData['content']);
        
        // Exact phrase match
        if (strpos($contentLower, $queryLower) !== false) {
            $score += 1.0;
        }
        
        // Word matches
        $queryWords = explode(' ', $queryLower);
        $contentWords = explode(' ', $contentLower);
        $matchingWords = array_intersect($queryWords, $contentWords);
        $score += (count($matchingWords) / count($queryWords)) * 0.5;
        
        // Tag matches
        foreach ($memoryData['tags'] as $tag) {
            if (stripos($query, $tag) !== false) {
                $score += 0.3;
            }
        }
        
        // Boost by importance
        $score *= (1 + $memoryData['importance']);
        
        return $score;
    }
}
