<?php

namespace App\Http\Controllers;

use App\Models\AgentSession;
use App\Models\SessionMessage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class SessionController extends Controller
{
    /**
     * Get all sessions for the authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user() ?? \App\Models\User::first();

        if (!$user) {
            return response()->json(['error' => 'No user found'], 401);
        }

        $sessions = $user->sessions()
            ->with('messages')
            ->orderBy('updated_at', 'desc')
            ->paginate(20);

        return response()->json($sessions);
    }

    /**
     * Create a new session.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'provider' => 'nullable|string|max:50',
            'model' => 'nullable|string|max:100',
            'context_data' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        // Get user (temporarily use first user for testing)
        $user = $request->user() ?? \App\Models\User::first();

        if (!$user) {
            return response()->json([
                'error' => 'No user found'
            ], 401);
        }

        $session = $user->sessions()->create([
            'session_uuid' => Str::uuid(),
            'title' => $request->title ?? 'New Session',
            'provider' => $request->provider,
            'model' => $request->model,
            'context_data' => $request->context_data ?? [],
            'memory_data' => [],
            'status' => 'active',
        ]);

        return response()->json([
            'session' => $session,
            'message' => 'Session created successfully'
        ], 201);
    }

    /**
     * Get a specific session.
     */
    public function show(Request $request, AgentSession $session): JsonResponse
    {
        // Ensure the session belongs to the authenticated user (temporarily disabled for testing)
        $user = $request->user() ?? \App\Models\User::first();
        if ($user && $session->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $session->load('messages');

        return response()->json(['session' => $session]);
    }

    /**
     * Update a session.
     */
    public function update(Request $request, AgentSession $session): JsonResponse
    {
        // Ensure the session belongs to the authenticated user (temporarily disabled for testing)
        $user = $request->user() ?? \App\Models\User::first();
        if ($user && $session->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'provider' => 'nullable|string|max:50',
            'model' => 'nullable|string|max:100',
            'context_data' => 'nullable|array',
            'memory_data' => 'nullable|array',
            'status' => 'nullable|in:active,paused,completed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $session->update($request->only([
            'title', 'provider', 'model', 'context_data', 'memory_data', 'status'
        ]));

        return response()->json([
            'session' => $session,
            'message' => 'Session updated successfully'
        ]);
    }

    /**
     * Delete a session.
     */
    public function destroy(Request $request, AgentSession $session): JsonResponse
    {
        // Ensure the session belongs to the authenticated user (temporarily disabled for testing)
        $user = $request->user() ?? \App\Models\User::first();
        if ($user && $session->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $session->delete();

        return response()->json([
            'message' => 'Session deleted successfully'
        ]);
    }

    /**
     * Add a message to a session.
     */
    public function addMessage(Request $request, AgentSession $session): JsonResponse
    {
        // Ensure the session belongs to the authenticated user (temporarily disabled for testing)
        $user = $request->user() ?? \App\Models\User::first();
        if ($user && $session->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'role' => 'required|in:user,assistant,system',
            'content' => 'required|string',
            'metadata' => 'nullable|array',
            'token_usage' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $message = $session->messages()->create([
            'role' => $request->role,
            'content' => $request->content,
            'metadata' => $request->metadata ?? [],
            'token_usage' => $request->token_usage ?? [],
        ]);

        return response()->json([
            'message' => $message,
            'success' => 'Message added successfully'
        ], 201);
    }
}
