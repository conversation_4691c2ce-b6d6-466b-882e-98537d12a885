# GOC IDE Branding Removal Guide

This guide explains how to remove Microsoft branding and telemetry from your VS Code fork to create GOC IDE.

## Overview

The branding removal process involves:
1. **Telemetry Removal**: Disable all Microsoft telemetry endpoints
2. **Product Configuration**: Replace product.json with GOC IDE settings
3. **Marketplace**: Switch from Microsoft marketplace to Open VSX
4. **Icons & Assets**: Replace Microsoft icons with GOC Agent branding
5. **Documentation**: Update all references and links

## Quick Start

```bash
# Make the script executable
chmod +x remove-branding.sh

# Run the branding removal script
./remove-branding.sh
```

## Manual Steps

### 1. Telemetry Removal

The script automatically removes these Microsoft telemetry endpoints:
- `vortex.data.microsoft.com`
- `mobile.events.data.microsoft.com`
- `*.data.microsoft.com`

These are replaced with `0.0.0.0` to disable telemetry completely.

### 2. Product Configuration

Replace `product.json` with `goc-product.json`:

```bash
cp goc-product.json product.json
```

Key changes in product.json:
- **Name**: "GOC IDE" instead of "Visual Studio Code"
- **Application ID**: `goc-ide` instead of `code`
- **Data Folder**: `.goc-ide` instead of `.vscode`
- **Extensions**: Uses Open VSX instead of Microsoft Marketplace
- **Telemetry**: Completely disabled
- **GOC Agent**: Custom configuration section added

### 3. Extension Marketplace

Switch from Microsoft's marketplace to Open VSX:
- **Service URL**: `https://open-vsx.org/vscode/gallery`
- **Item URL**: `https://open-vsx.org/vscode/item`
- **Control URL**: Empty (disables Microsoft control)

### 4. Icons and Assets

Replace these files in `resources/` directory:
- `resources/win32/code.ico` → GOC IDE icon
- `resources/linux/code.png` → GOC IDE icon
- `resources/darwin/code.icns` → GOC IDE icon
- `resources/server/code-web.ico` → GOC IDE web icon

### 5. Splash Screen

Update splash screen files:
- `src/vs/workbench/browser/parts/splash/` → GOC IDE splash
- Update loading messages and branding

## Files Modified

### Core Configuration
- `product.json` - Main product configuration
- `package.json` - Package metadata

### Telemetry Files
- `src/vs/platform/telemetry/` - Telemetry services
- `src/vs/base/common/product.ts` - Product definitions
- `src/vs/platform/update/` - Update services

### UI Elements
- `src/vs/workbench/browser/workbench.ts` - Main workbench
- `src/vs/workbench/browser/parts/titlebar/` - Title bar
- `src/vs/workbench/contrib/welcome/` - Welcome screen

### Extension System
- `src/vs/platform/extensionManagement/` - Extension management
- `src/vs/workbench/services/extensionManagement/` - Extension services

## Verification

After running the script, verify the changes:

```bash
# Check product.json
grep -i "goc" product.json

# Verify telemetry removal
grep -r "data.microsoft.com" . || echo "Telemetry removed successfully"

# Check marketplace configuration
grep -A 5 "extensionsGallery" product.json
```

## Build and Test

```bash
# Install dependencies
npm install

# Build the application
npm run compile

# Run in development mode
npm run watch

# Start GOC IDE
./scripts/code.sh
```

## Troubleshooting

### Script Permissions
```bash
chmod +x remove-branding.sh
```

### Backup Recovery
If something goes wrong, restore from backup:
```bash
cp product.json.backup product.json
```

### Missing Dependencies
```bash
# Install ripgrep for faster search
npm install @vscode/ripgrep
```

## Next Steps

After branding removal:
1. **Apply GOC Agent Branding** - Add custom icons and themes
2. **Integrate Backend API** - Connect to Laravel backend
3. **Build Chat Panel** - Add GOC Agent AI interface
4. **Create Extensions** - Develop GOC-specific extensions
5. **Package Distribution** - Build installers for all platforms

## Important Notes

- Always backup `product.json` before making changes
- Test thoroughly after branding removal
- Update GitHub URLs with your actual repository
- Consider legal implications of using VS Code source
- Keep track of upstream changes for future updates

## VSCodium Reference

This approach is based on the successful VSCodium project:
- Repository: https://github.com/VSCodium/vscodium
- Documentation: https://vscodium.com
- Build Scripts: https://github.com/VSCodium/vscodium/blob/master/build.sh
