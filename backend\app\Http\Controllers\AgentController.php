<?php

namespace App\Http\Controllers;

use App\Models\AgentSession;
use App\Models\ApiUsage;
use App\Services\AIProviderService;
use App\Services\ToolService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;

class AgentController extends Controller
{
    protected AIProviderService $aiProvider;
    protected ToolService $toolService;

    public function __construct(AIProviderService $aiProvider, ToolService $toolService)
    {
        $this->aiProvider = $aiProvider;
        $this->toolService = $toolService;
    }

    /**
     * Handle chat requests.
     */
    public function chat(Request $request): JsonResponse
    {
        $startTime = microtime(true);

        $validator = Validator::make($request->all(), [
            'session_id' => 'required|exists:agent_sessions,id',
            'message' => 'required|string',
            'provider' => 'nullable|string',
            'model' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $session = AgentSession::findOrFail($request->session_id);

        // Ensure the session belongs to the authenticated user (temporarily disabled for testing)
        $user = $request->user() ?? \App\Models\User::first();
        if ($user && $session->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Add user message to session
        $userMessage = $session->messages()->create([
            'role' => 'user',
            'content' => $request->message,
            'metadata' => [],
        ]);

        // Prepare messages for AI provider
        $messages = $session->messages()
            ->orderBy('created_at')
            ->get()
            ->map(function ($msg) {
                return [
                    'role' => $msg->role,
                    'content' => $msg->content,
                ];
            })
            ->toArray();

        // Get AI response
        $provider = $request->provider ?? $session->provider ?? 'ollama';
        $model = $request->model ?? $session->model ?? 'llama2';

        $aiResult = $this->aiProvider->chat($provider, $model, $messages);

        if ($aiResult['error']) {
            return response()->json([
                'error' => 'AI provider error',
                'message' => $aiResult['message']
            ], 500);
        }

        // Add AI response to session
        $assistantMessage = $session->messages()->create([
            'role' => 'assistant',
            'content' => $aiResult['content'],
            'metadata' => [
                'provider' => $provider,
                'model' => $model,
            ],
            'token_usage' => $aiResult['token_usage'],
        ]);

        // Update session with latest provider/model
        $session->update([
            'provider' => $provider,
            'model' => $model,
        ]);

        // Log API usage
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000);

        $user = $request->user() ?? \App\Models\User::first();
        if ($user) {
            $user->apiUsage()->create([
            'endpoint' => '/api/agent/chat',
            'method' => 'POST',
            'status_code' => 200,
            'response_time_ms' => $responseTime,
            'tokens_used' => $aiResult['token_usage']['total_tokens'] ?? 0,
            'provider' => $provider,
            'model' => $model,
        ]);
        }

        return response()->json([
            'user_message' => $userMessage,
            'assistant_message' => $assistantMessage,
            'session' => $session->fresh(),
        ]);
    }

    /**
     * Execute a task.
     */
    public function executeTask(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'task' => 'required|string',
            'session_id' => 'nullable|exists:agent_sessions,id',
            'provider' => 'nullable|string',
            'model' => 'nullable|string',
            'auto_mode' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        // TODO: Implement task execution logic
        return response()->json([
            'message' => 'Task execution will be implemented in the next phase',
            'task' => $request->task,
            'status' => 'pending'
        ]);
    }

    /**
     * Get agent status.
     */
    public function status(Request $request): JsonResponse
    {
        $user = $request->user() ?? \App\Models\User::first();

        if (!$user) {
            return response()->json(['error' => 'No user found'], 401);
        }

        $stats = [
            'total_sessions' => $user->sessions()->count(),
            'active_sessions' => $user->sessions()->where('status', 'active')->count(),
            'total_messages' => $user->sessions()->withCount('messages')->get()->sum('messages_count'),
            'api_usage_today' => $user->apiUsage()->whereDate('created_at', today())->count(),
            'tokens_used_today' => $user->apiUsage()->whereDate('created_at', today())->sum('tokens_used'),
            'quota_remaining' => $user->api_quota_daily - $user->apiUsage()->whereDate('created_at', today())->count(),
        ];

        return response()->json([
            'status' => 'online',
            'user' => $user,
            'statistics' => $stats,
        ]);
    }

    /**
     * Execute a specific tool.
     */
    public function executeTool(Request $request, string $tool): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'parameters' => 'nullable|array',
            'session_id' => 'nullable|exists:agent_sessions,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $startTime = microtime(true);

        // Execute the tool
        $result = $this->toolService->executeTool($tool, $request->parameters ?? [], $request->user());

        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000);

        // Log API usage (temporarily disabled for testing)
        $user = $request->user() ?? \App\Models\User::first();
        if ($user) {
            $user->apiUsage()->create([
            'endpoint' => "/api/agent/tools/{$tool}",
            'method' => 'POST',
            'status_code' => $result['success'] ? 200 : 500,
            'response_time_ms' => $responseTime,
            'tokens_used' => 0,
            'provider' => 'tool',
            'model' => $tool,
        ]);
        }

        if (!$result['success']) {
            return response()->json([
                'error' => $result['error'],
                'tool' => $tool,
                'parameters' => $request->parameters ?? [],
            ], 500);
        }

        return response()->json([
            'success' => true,
            'tool' => $tool,
            'result' => $result['data'] ?? $result,
            'execution_time_ms' => $responseTime,
        ]);
    }

    /**
     * Get available AI providers and models.
     */
    public function providers(Request $request): JsonResponse
    {
        $providers = [];

        foreach ($this->aiProvider->getProviders() as $provider) {
            $providers[$provider] = [
                'name' => $provider,
                'available' => $this->aiProvider->isProviderAvailable($provider),
                'models' => $this->aiProvider->getModels($provider),
            ];
        }

        return response()->json([
            'providers' => $providers,
        ]);
    }
}
