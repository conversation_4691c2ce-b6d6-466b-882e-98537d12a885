<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_index', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('project_path', 500);
            $table->string('file_path', 500);
            $table->string('file_type', 50)->nullable();
            $table->string('language', 50)->nullable();
            $table->integer('size_bytes')->nullable();
            $table->integer('line_count')->nullable();
            $table->integer('function_count')->nullable();
            $table->integer('class_count')->nullable();
            $table->timestamp('last_modified')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'project_path']);
            $table->index('language');
            $table->index('file_type');
            $table->index(['file_path', 'last_modified']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_index');
    }
};
