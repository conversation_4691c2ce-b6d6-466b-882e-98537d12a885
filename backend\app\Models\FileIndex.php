<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FileIndex extends Model
{
    use HasFactory;

    protected $table = 'file_index';

    protected $fillable = [
        'user_id',
        'project_path',
        'file_path',
        'file_type',
        'language',
        'size_bytes',
        'line_count',
        'function_count',
        'class_count',
        'last_modified',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'last_modified' => 'datetime',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the user that owns the file index.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
