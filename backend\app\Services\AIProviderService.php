<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AIProviderService
{
    protected array $providers = [
        'openai' => [
            'base_url' => 'https://api.openai.com/v1',
            'models' => ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        ],
        'groq' => [
            'base_url' => 'https://api.groq.com/openai/v1',
            'models' => ['llama2-70b-4096', 'mixtral-8x7b-32768'],
        ],
        'gemini' => [
            'base_url' => 'https://generativelanguage.googleapis.com/v1beta',
            'models' => ['gemini-pro', 'gemini-pro-vision'],
        ],
        'ollama' => [
            'base_url' => 'http://localhost:11434',
            'models' => ['llama2', 'codellama', 'mistral'],
        ],
    ];

    /**
     * Send a chat request to the specified AI provider.
     */
    public function chat(string $provider, string $model, array $messages, array $options = []): array
    {
        try {
            switch ($provider) {
                case 'openai':
                case 'groq':
                    return $this->sendOpenAICompatibleRequest($provider, $model, $messages, $options);
                
                case 'gemini':
                    return $this->sendGeminiRequest($model, $messages, $options);
                
                case 'ollama':
                    return $this->sendOllamaRequest($model, $messages, $options);
                
                default:
                    throw new \InvalidArgumentException("Unsupported provider: {$provider}");
            }
        } catch (\Exception $e) {
            Log::error("AI Provider Error", [
                'provider' => $provider,
                'model' => $model,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'error' => true,
                'message' => 'AI provider request failed: ' . $e->getMessage(),
                'content' => null,
                'token_usage' => ['prompt_tokens' => 0, 'completion_tokens' => 0, 'total_tokens' => 0],
            ];
        }
    }

    /**
     * Send request to OpenAI-compatible APIs (OpenAI, Groq).
     */
    protected function sendOpenAICompatibleRequest(string $provider, string $model, array $messages, array $options): array
    {
        $config = $this->providers[$provider];
        $apiKey = $this->getApiKey($provider);
        
        if (!$apiKey) {
            throw new \Exception("API key not configured for provider: {$provider}");
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$apiKey}",
            'Content-Type' => 'application/json',
        ])->timeout(120)->post("{$config['base_url']}/chat/completions", [
            'model' => $model,
            'messages' => $messages,
            'temperature' => $options['temperature'] ?? 0.7,
            'max_tokens' => $options['max_tokens'] ?? 4000,
            'top_p' => $options['top_p'] ?? 1.0,
        ]);

        if (!$response->successful()) {
            throw new \Exception("API request failed: " . $response->body());
        }

        $data = $response->json();
        
        return [
            'error' => false,
            'content' => $data['choices'][0]['message']['content'] ?? '',
            'token_usage' => $data['usage'] ?? ['prompt_tokens' => 0, 'completion_tokens' => 0, 'total_tokens' => 0],
            'model' => $data['model'] ?? $model,
            'provider' => $provider,
        ];
    }

    /**
     * Send request to Google Gemini API.
     */
    protected function sendGeminiRequest(string $model, array $messages, array $options): array
    {
        $apiKey = $this->getApiKey('gemini');
        
        if (!$apiKey) {
            throw new \Exception("API key not configured for provider: gemini");
        }

        // Convert messages to Gemini format
        $contents = [];
        foreach ($messages as $message) {
            $contents[] = [
                'role' => $message['role'] === 'assistant' ? 'model' : 'user',
                'parts' => [['text' => $message['content']]],
            ];
        }

        $response = Http::timeout(120)->post(
            "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}",
            [
                'contents' => $contents,
                'generationConfig' => [
                    'temperature' => $options['temperature'] ?? 0.7,
                    'maxOutputTokens' => $options['max_tokens'] ?? 4000,
                ],
            ]
        );

        if (!$response->successful()) {
            throw new \Exception("Gemini API request failed: " . $response->body());
        }

        $data = $response->json();
        
        return [
            'error' => false,
            'content' => $data['candidates'][0]['content']['parts'][0]['text'] ?? '',
            'token_usage' => [
                'prompt_tokens' => $data['usageMetadata']['promptTokenCount'] ?? 0,
                'completion_tokens' => $data['usageMetadata']['candidatesTokenCount'] ?? 0,
                'total_tokens' => $data['usageMetadata']['totalTokenCount'] ?? 0,
            ],
            'model' => $model,
            'provider' => 'gemini',
        ];
    }

    /**
     * Send request to Ollama API.
     */
    protected function sendOllamaRequest(string $model, array $messages, array $options): array
    {
        $response = Http::timeout(120)->post('http://localhost:11434/api/chat', [
            'model' => $model,
            'messages' => $messages,
            'stream' => false,
            'options' => [
                'temperature' => $options['temperature'] ?? 0.7,
                'num_predict' => $options['max_tokens'] ?? 4000,
            ],
        ]);

        if (!$response->successful()) {
            throw new \Exception("Ollama API request failed: " . $response->body());
        }

        $data = $response->json();
        
        return [
            'error' => false,
            'content' => $data['message']['content'] ?? '',
            'token_usage' => [
                'prompt_tokens' => $data['prompt_eval_count'] ?? 0,
                'completion_tokens' => $data['eval_count'] ?? 0,
                'total_tokens' => ($data['prompt_eval_count'] ?? 0) + ($data['eval_count'] ?? 0),
            ],
            'model' => $model,
            'provider' => 'ollama',
        ];
    }

    /**
     * Get API key for the specified provider.
     */
    protected function getApiKey(string $provider): ?string
    {
        return match ($provider) {
            'openai' => env('OPENAI_API_KEY'),
            'groq' => env('GROQ_API_KEY'),
            'gemini' => env('GEMINI_API_KEY'),
            'ollama' => null, // Ollama doesn't require API key
            default => null,
        };
    }

    /**
     * Get available models for a provider.
     */
    public function getModels(string $provider): array
    {
        return $this->providers[$provider]['models'] ?? [];
    }

    /**
     * Get all available providers.
     */
    public function getProviders(): array
    {
        return array_keys($this->providers);
    }

    /**
     * Check if a provider is available.
     */
    public function isProviderAvailable(string $provider): bool
    {
        if (!isset($this->providers[$provider])) {
            return false;
        }

        // For Ollama, check if the service is running
        if ($provider === 'ollama') {
            try {
                $response = Http::timeout(5)->get('http://localhost:11434/api/tags');
                return $response->successful();
            } catch (\Exception $e) {
                return false;
            }
        }

        // For other providers, check if API key is configured
        return !empty($this->getApiKey($provider));
    }
}
