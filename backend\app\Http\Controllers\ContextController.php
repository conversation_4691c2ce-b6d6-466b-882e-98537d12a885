<?php

namespace App\Http\Controllers;

use App\Services\ContextEngineService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ContextController extends Controller
{
    protected ContextEngineService $contextEngine;

    public function __construct(ContextEngineService $contextEngine)
    {
        $this->contextEngine = $contextEngine;
    }

    /**
     * Index a project for context search.
     */
    public function indexProject(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'project_path' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $result = $this->contextEngine->indexProject(
            $request->user(),
            $request->project_path
        );

        if (!$result['success']) {
            return response()->json([
                'error' => $result['error']
            ], 500);
        }

        return response()->json([
            'success' => true,
            'data' => $result['data'],
            'message' => $result['message']
        ]);
    }

    /**
     * Search for code using semantic similarity.
     */
    public function searchCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string',
            'project_path' => 'nullable|string',
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $result = $this->contextEngine->searchCode(
            $request->user(),
            $request->query,
            $request->project_path,
            $request->limit ?? 10
        );

        if (!$result['success']) {
            return response()->json([
                'error' => $result['error']
            ], 500);
        }

        return response()->json([
            'success' => true,
            'data' => $result['data']
        ]);
    }

    /**
     * Get project statistics.
     */
    public function getProjectStats(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'project_path' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $projectPath = $request->project_path;

        $fileIndexQuery = $user->fileIndex();
        $embeddingsQuery = $user->codeEmbeddings();

        if ($projectPath) {
            $fileIndexQuery->where('project_path', $projectPath);
            $embeddingsQuery->where('project_path', $projectPath);
        }

        $stats = [
            'total_files' => $fileIndexQuery->count(),
            'total_embeddings' => $embeddingsQuery->count(),
            'languages' => $fileIndexQuery->distinct('language')->pluck('language'),
            'total_lines' => $fileIndexQuery->sum('line_count'),
            'total_functions' => $fileIndexQuery->sum('function_count'),
            'total_classes' => $fileIndexQuery->sum('class_count'),
            'projects' => $user->fileIndex()->distinct('project_path')->pluck('project_path'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
