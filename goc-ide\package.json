{"name": "goc-ide", "version": "1.0.0", "description": "GOC IDE - VS Code Fork for GOC Agent with AI integration", "private": true, "scripts": {"setup": "echo 'Please follow SETUP.md instructions to clone VS Code fork'", "help": "echo 'Run: npm run setup for initial setup instructions'"}, "keywords": ["ide", "editor", "goc-agent", "vscode-fork", "ai-coding"], "author": "GOC Agent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/YOUR_USERNAME/vscode.git"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}