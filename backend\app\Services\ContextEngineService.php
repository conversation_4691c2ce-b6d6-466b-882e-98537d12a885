<?php

namespace App\Services;

use App\Models\User;
use App\Models\CodeEmbedding;
use App\Models\FileIndex;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ContextEngineService
{
    protected array $supportedLanguages = [
        'php', 'js', 'ts', 'py', 'java', 'cpp', 'c', 'cs', 'go', 'rs', 'rb', 'swift', 'kt'
    ];

    protected array $codeExtensions = [
        'php' => ['php'],
        'javascript' => ['js', 'jsx', 'mjs'],
        'typescript' => ['ts', 'tsx'],
        'python' => ['py', 'pyw'],
        'java' => ['java'],
        'cpp' => ['cpp', 'cxx', 'cc', 'c++'],
        'c' => ['c', 'h'],
        'csharp' => ['cs'],
        'go' => ['go'],
        'rust' => ['rs'],
        'ruby' => ['rb'],
        'swift' => ['swift'],
        'kotlin' => ['kt', 'kts'],
    ];

    /**
     * Index a project for the given user.
     */
    public function indexProject(User $user, string $projectPath): array
    {
        try {
            $stats = [
                'files_processed' => 0,
                'files_indexed' => 0,
                'embeddings_created' => 0,
                'errors' => [],
            ];

            // Clean existing index for this project
            $user->fileIndex()->where('project_path', $projectPath)->delete();
            $user->codeEmbeddings()->where('project_path', $projectPath)->delete();

            // Scan and index files
            $this->scanDirectory($user, $projectPath, $projectPath, $stats);

            return [
                'success' => true,
                'data' => $stats,
                'message' => 'Project indexed successfully',
            ];
        } catch (\Exception $e) {
            Log::error('Context indexing error', [
                'user_id' => $user->id,
                'project_path' => $projectPath,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Recursively scan directory and index files.
     */
    protected function scanDirectory(User $user, string $projectPath, string $currentPath, array &$stats): void
    {
        if (!File::exists($currentPath) || !File::isDirectory($currentPath)) {
            return;
        }

        $items = File::glob($currentPath . '/*');

        foreach ($items as $item) {
            $relativePath = str_replace($projectPath . '/', '', $item);
            
            // Skip hidden files and common ignore patterns
            if ($this->shouldIgnore($relativePath)) {
                continue;
            }

            if (File::isDirectory($item)) {
                $this->scanDirectory($user, $projectPath, $item, $stats);
            } else {
                $this->indexFile($user, $projectPath, $item, $stats);
            }
        }
    }

    /**
     * Index a single file.
     */
    protected function indexFile(User $user, string $projectPath, string $filePath, array &$stats): void
    {
        try {
            $stats['files_processed']++;
            
            $relativePath = str_replace($projectPath . '/', '', $filePath);
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            $language = $this->detectLanguage($extension);
            
            // Only index code files
            if (!$language) {
                return;
            }

            $content = File::get($filePath);
            $contentHash = hash('sha256', $content);
            $size = File::size($filePath);
            $lastModified = File::lastModified($filePath);

            // Analyze code structure
            $analysis = $this->analyzeCode($content, $language);

            // Create file index entry
            $user->fileIndex()->create([
                'project_path' => $projectPath,
                'file_path' => $relativePath,
                'file_type' => $extension,
                'language' => $language,
                'size_bytes' => $size,
                'line_count' => substr_count($content, "\n") + 1,
                'function_count' => $analysis['function_count'],
                'class_count' => $analysis['class_count'],
                'last_modified' => date('Y-m-d H:i:s', $lastModified),
                'metadata' => [
                    'imports' => $analysis['imports'],
                    'exports' => $analysis['exports'],
                    'functions' => $analysis['functions'],
                    'classes' => $analysis['classes'],
                ],
            ]);

            // Create embeddings for code chunks
            $chunks = $this->chunkCode($content, $language);
            foreach ($chunks as $chunk) {
                $embedding = $this->generateEmbedding($chunk['content']);
                if ($embedding) {
                    $user->codeEmbeddings()->create([
                        'project_path' => $projectPath,
                        'file_path' => $relativePath,
                        'content_hash' => hash('sha256', $chunk['content']),
                        'embedding_vector' => $embedding,
                        'metadata' => [
                            'chunk_type' => $chunk['type'],
                            'start_line' => $chunk['start_line'],
                            'end_line' => $chunk['end_line'],
                            'language' => $language,
                        ],
                    ]);
                    $stats['embeddings_created']++;
                }
            }

            $stats['files_indexed']++;
        } catch (\Exception $e) {
            $stats['errors'][] = "Error indexing {$filePath}: " . $e->getMessage();
            Log::error('File indexing error', [
                'file_path' => $filePath,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if file/directory should be ignored.
     */
    protected function shouldIgnore(string $path): bool
    {
        $ignorePatterns = [
            '.git', '.svn', '.hg',
            'node_modules', 'vendor', 'dist', 'build',
            '.env', '.env.*',
            '*.log', '*.tmp', '*.cache',
            '.DS_Store', 'Thumbs.db',
        ];

        foreach ($ignorePatterns as $pattern) {
            if (fnmatch($pattern, basename($path)) || fnmatch($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect programming language from file extension.
     */
    protected function detectLanguage(string $extension): ?string
    {
        foreach ($this->codeExtensions as $language => $extensions) {
            if (in_array(strtolower($extension), $extensions)) {
                return $language;
            }
        }
        return null;
    }

    /**
     * Analyze code structure (basic implementation).
     */
    protected function analyzeCode(string $content, string $language): array
    {
        $analysis = [
            'function_count' => 0,
            'class_count' => 0,
            'imports' => [],
            'exports' => [],
            'functions' => [],
            'classes' => [],
        ];

        // Basic regex patterns for different languages
        $patterns = [
            'php' => [
                'function' => '/function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/m',
                'class' => '/class\s+([a-zA-Z_][a-zA-Z0-9_]*)/m',
                'import' => '/(?:use|require|include)\s+([^;]+);/m',
            ],
            'javascript' => [
                'function' => '/(?:function\s+([a-zA-Z_][a-zA-Z0-9_]*)|const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(?:\([^)]*\)\s*=>|function))/m',
                'class' => '/class\s+([a-zA-Z_][a-zA-Z0-9_]*)/m',
                'import' => '/import\s+.*?from\s+[\'"]([^\'"]+)[\'"]/m',
            ],
            'python' => [
                'function' => '/def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/m',
                'class' => '/class\s+([a-zA-Z_][a-zA-Z0-9_]*)/m',
                'import' => '/(?:import|from)\s+([a-zA-Z_][a-zA-Z0-9_.]*)/m',
            ],
        ];

        if (isset($patterns[$language])) {
            $langPatterns = $patterns[$language];
            
            // Count functions
            if (preg_match_all($langPatterns['function'], $content, $matches)) {
                $analysis['function_count'] = count($matches[0]);
                $analysis['functions'] = array_filter($matches[1] ?: $matches[2] ?: []);
            }
            
            // Count classes
            if (preg_match_all($langPatterns['class'], $content, $matches)) {
                $analysis['class_count'] = count($matches[0]);
                $analysis['classes'] = $matches[1];
            }
            
            // Extract imports
            if (preg_match_all($langPatterns['import'], $content, $matches)) {
                $analysis['imports'] = $matches[1];
            }
        }

        return $analysis;
    }

    /**
     * Chunk code into meaningful segments.
     */
    protected function chunkCode(string $content, string $language): array
    {
        $lines = explode("\n", $content);
        $chunks = [];
        $currentChunk = [];
        $chunkSize = 50; // lines per chunk
        
        for ($i = 0; $i < count($lines); $i += $chunkSize) {
            $chunkLines = array_slice($lines, $i, $chunkSize);
            $chunks[] = [
                'content' => implode("\n", $chunkLines),
                'type' => 'code_block',
                'start_line' => $i + 1,
                'end_line' => min($i + $chunkSize, count($lines)),
            ];
        }

        return $chunks;
    }

    /**
     * Generate embedding for code content (placeholder implementation).
     */
    protected function generateEmbedding(string $content): ?array
    {
        // This is a placeholder. In a real implementation, you would:
        // 1. Use a local embedding model like CodeBERT
        // 2. Call an embedding API service
        // 3. Use a vector database like Pinecone or Weaviate
        
        // For now, return a simple hash-based vector
        $hash = hash('sha256', $content);
        $vector = [];
        for ($i = 0; $i < 384; $i++) { // 384-dimensional vector
            $vector[] = (float) (hexdec(substr($hash, $i % 64, 2)) / 255.0);
        }
        
        return $vector;
    }

    /**
     * Search for relevant code using semantic similarity.
     */
    public function searchCode(User $user, string $query, string $projectPath = null, int $limit = 10): array
    {
        try {
            // Generate query embedding
            $queryEmbedding = $this->generateEmbedding($query);
            if (!$queryEmbedding) {
                return ['success' => false, 'error' => 'Failed to generate query embedding'];
            }

            // For now, return a simple text-based search
            // In a real implementation, you would use vector similarity search
            $results = $user->fileIndex()
                ->when($projectPath, function ($query) use ($projectPath) {
                    return $query->where('project_path', $projectPath);
                })
                ->where(function ($q) use ($query) {
                    $q->whereJsonContains('metadata->functions', $query)
                      ->orWhereJsonContains('metadata->classes', $query)
                      ->orWhere('file_path', 'like', "%{$query}%");
                })
                ->limit($limit)
                ->get();

            return [
                'success' => true,
                'data' => [
                    'query' => $query,
                    'results' => $results,
                    'total_results' => $results->count(),
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Code search error', [
                'user_id' => $user->id,
                'query' => $query,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        }
    }
}
